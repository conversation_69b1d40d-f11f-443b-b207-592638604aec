0.3.0 (not yet released)
      breaking: add max_settled_nodes parameters to Params, important performance tuning for graphs with large-weight edges, #37
      add InputGraph::to_file, #35
      faster fast_graph building, 82e9a2ef417af6de1b2cb41bcee41b6302db1b4a
      allow passing &[T] instead of &Vec<T> in a few places, #32
      add calc_path_multiple_sources_and_targets, #30
0.2.0 [April 25th 2021]
      add clone and copy derives, #26
      faster fast_graph building
      faster queries (stall on demand optimization), #18
      fix serious performance regression for Rust >=1.45, #21
      remove rand and bincode dependencies, #17
      allow storing the graph for 32bit systems on a 64bit system, #14
      WebAssembly compatibility, #11
      deterministic fast_graph building, #10
      license change to dual MIT/Apache 2.0, #4
0.1.1 [June 17th 2019]
      adds travis and meta information to Cargo.toml
0.1.0 [June 17th 2019]
      initial version