[package]
name = "fast_paths"
version = "0.3.0-SNAPSHOT"
authors = ["easbar <<EMAIL>>"]
edition = "2018"
description = "Fast shortest path calculations on directed graphs made possible by pre-processing the graph using Contraction Hierarchies"
repository = "https://github.com/easbar/fast_paths"
categories = ["algorithms", "data-structures", "science"]
license = "MIT/Apache-2.0"

[badges]
travis-ci = { repository = "easbar/fast_paths", branch = "master" }

[dependencies]
serde = { version = "1.0", features =["derive"] }
log = "0.4"
priority-queue = "1.0.0"

[dev-dependencies]
bincode = "1.1.2"
rand = "0.6"
stopwatch = "0.0.7"
