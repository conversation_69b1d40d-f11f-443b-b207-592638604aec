[tool.poetry]
name = "pinpoint"
version = "0.1.0"
description = ""
authors = ["none"]
packages = [
    { include = "pinpoint" },
]

[tool.poetry.dependencies]
python = "^3.8"
requests = "^2.27.1"
pydantic = "^1.9.0"
requests-cache = "^0.9.3"
python-dotenv = "^0.20.0"

[tool.poetry.dev-dependencies]
jupyter = "^1.0.0"
ipykernel = "^6.13.0"
devtools = "^0.8.0"
autopep8 = "^1.6.0"
pytest = "^7.1.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
