/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, <PERSON>bug, PartialEq, De<PERSON>ult, <PERSON>ialize, Deserialize)]
pub struct ResponseTimeFilterPostcodeDistricts {
    #[serde(rename = "results")]
    pub results: Vec<crate::models::ResponseTimeFilterPostcodeDistrictsResult>,
}

impl ResponseTimeFilterPostcodeDistricts {
    pub fn new(results: Vec<crate::models::ResponseTimeFilterPostcodeDistrictsResult>) -> ResponseTimeFilterPostcodeDistricts {
        ResponseTimeFilterPostcodeDistricts {
            results,
        }
    }
}


