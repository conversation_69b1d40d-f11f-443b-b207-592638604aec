/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, Default, Serialize, Deserialize)]
pub struct ResponseMapInfoFeaturesPublicTransport {
    #[serde(rename = "date_start")]
    pub date_start: String,
    #[serde(rename = "date_end")]
    pub date_end: String,
}

impl ResponseMapInfoFeaturesPublicTransport {
    pub fn new(date_start: String, date_end: String) -> ResponseMapInfoFeaturesPublicTransport {
        ResponseMapInfoFeaturesPublicTransport {
            date_start,
            date_end,
        }
    }
}


