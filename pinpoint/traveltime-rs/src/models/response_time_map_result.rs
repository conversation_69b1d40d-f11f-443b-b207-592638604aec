/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseTimeMapResult {
    #[serde(rename = "search_id")]
    pub search_id: String,
    #[serde(rename = "shapes")]
    pub shapes: Vec<crate::models::ResponseShape>,
    #[serde(rename = "properties")]
    pub properties: Box<crate::models::ResponseTimeMapProperties>,
}

impl ResponseTimeMapResult {
    pub fn new(search_id: String, shapes: Vec<crate::models::ResponseShape>, properties: crate::models::ResponseTimeMapProperties) -> ResponseTimeMapResult {
        ResponseTimeMapResult {
            search_id,
            shapes,
            properties: Box::new(properties),
        }
    }
}


