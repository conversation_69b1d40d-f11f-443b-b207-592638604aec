/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseBoundingBox {
    #[serde(rename = "envelope")]
    pub envelope: Box<crate::models::ResponseBox>,
    #[serde(rename = "boxes")]
    pub boxes: Vec<crate::models::ResponseBox>,
}

impl ResponseBoundingBox {
    pub fn new(envelope: crate::models::ResponseBox, boxes: Vec<crate::models::ResponseBox>) -> ResponseBoundingBox {
        ResponseBoundingBox {
            envelope: Box::new(envelope),
            boxes,
        }
    }
}


