/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, De<PERSON>ult, <PERSON>ialize, Deserialize)]
pub struct ResponseTimeFilterPostcodeSector {
    #[serde(rename = "code")]
    pub code: String,
    #[serde(rename = "properties")]
    pub properties: Box<crate::models::ResponseTimeFilterPostcodeSectorProperties>,
}

impl ResponseTimeFilterPostcodeSector {
    pub fn new(code: String, properties: crate::models::ResponseTimeFilterPostcodeSectorProperties) -> ResponseTimeFilterPostcodeSector {
        ResponseTimeFilterPostcodeSector {
            code,
            properties: Box::new(properties),
        }
    }
}


