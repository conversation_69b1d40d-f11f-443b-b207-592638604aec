/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


use leaflet::LatLng;

#[derive(Clone, Debug, PartialEq, Default, Serialize, Deserialize, Copy)]
pub struct Coords {
    #[serde(rename = "lat")]
    pub lat: f64,
    #[serde(rename = "lng")]
    pub lng: f64,
}

impl Coords {
    pub fn new(lat: f64, lng: f64) -> Coords {
        Coords {
            lat,
            lng,
        }
    }
}

impl From<Coords> for LatLng {
    fn from(coords: Coords) -> Self {
        LatLng::new(coords.lat, coords.lng)
    }
}
