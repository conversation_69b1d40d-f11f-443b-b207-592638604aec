/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseMapInfo {
    #[serde(rename = "maps")]
    pub maps: Vec<crate::models::ResponseMapInfoMap>,
}

impl ResponseMapInfo {
    pub fn new(maps: Vec<crate::models::ResponseMapInfoMap>) -> ResponseMapInfo {
        ResponseMapInfo {
            maps,
        }
    }
}


