/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, PartialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseTimeFilterPostcodesProperties {
    #[serde(rename = "travel_time", skip_serializing_if = "Option::is_none")]
    pub travel_time: Option<i32>,
    #[serde(rename = "distance", skip_serializing_if = "Option::is_none")]
    pub distance: Option<i32>,
}

impl ResponseTimeFilterPostcodesProperties {
    pub fn new() -> ResponseTimeFilterPostcodesProperties {
        ResponseTimeFilterPostcodesProperties {
            travel_time: None,
            distance: None,
        }
    }
}


