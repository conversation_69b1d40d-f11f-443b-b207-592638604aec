/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, PartialEq, Default, Serialize, Deserialize)]
pub struct RequestTimeFilter {
    #[serde(rename = "locations")]
    pub locations: Vec<crate::models::RequestLocation>,
    #[serde(rename = "departure_searches", skip_serializing_if = "Option::is_none")]
    pub departure_searches: Option<Vec<crate::models::RequestTimeFilterDepartureSearch>>,
    #[serde(rename = "arrival_searches", skip_serializing_if = "Option::is_none")]
    pub arrival_searches: Option<Vec<crate::models::RequestTimeFilterArrivalSearch>>,
}

impl RequestTimeFilter {
    pub fn new(locations: Vec<crate::models::RequestLocation>) -> RequestTimeFilter {
        RequestTimeFilter {
            locations,
            departure_searches: None,
            arrival_searches: None,
        }
    }
}


