/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseRoute {
    #[serde(rename = "departure_time")]
    pub departure_time: String,
    #[serde(rename = "arrival_time")]
    pub arrival_time: String,
    #[serde(rename = "parts")]
    pub parts: Vec<crate::models::ResponseRoutePart>,
}

impl ResponseRoute {
    pub fn new(departure_time: String, arrival_time: String, parts: Vec<crate::models::ResponseRoutePart>) -> ResponseRoute {
        ResponseRoute {
            departure_time,
            arrival_time,
            parts,
        }
    }
}


