/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, <PERSON>bug, PartialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseGeocodingGeoJsonFeature {
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "geometry")]
    pub geometry: Box<crate::models::ResponseGeocodingGeometry>,
    #[serde(rename = "properties")]
    pub properties: Box<crate::models::ResponseGeocodingProperties>,
}

impl ResponseGeocodingGeoJsonFeature {
    pub fn new(r#type: String, geometry: crate::models::ResponseGeocodingGeometry, properties: crate::models::ResponseGeocodingProperties) -> ResponseGeocodingGeoJsonFeature {
        ResponseGeocodingGeoJsonFeature {
            r#type,
            geometry: Box::new(geometry),
            properties: Box::new(properties),
        }
    }
}


