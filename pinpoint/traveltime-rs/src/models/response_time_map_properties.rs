/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseTimeMapProperties {
    #[serde(rename = "is_only_walking", skip_serializing_if = "Option::is_none")]
    pub is_only_walking: Option<bool>,
}

impl ResponseTimeMapProperties {
    pub fn new() -> ResponseTimeMapProperties {
        ResponseTimeMapProperties {
            is_only_walking: None,
        }
    }
}


