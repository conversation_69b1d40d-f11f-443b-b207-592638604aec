/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, Debug, <PERSON>ialEq, Default, Serialize, Deserialize)]
pub struct RequestTimeFilterPostcodesArrivalSearch {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "transportation")]
    pub transportation: Box<crate::models::RequestTransportation>,
    #[serde(rename = "travel_time")]
    pub travel_time: i32,
    #[serde(rename = "arrival_time")]
    pub arrival_time: String,
    #[serde(rename = "properties")]
    pub properties: Vec<crate::models::RequestTimeFilterPostcodesProperty>,
    #[serde(rename = "range", skip_serializing_if = "Option::is_none")]
    pub range: Option<Box<crate::models::RequestRangeFull>>,
}

impl RequestTimeFilterPostcodesArrivalSearch {
    pub fn new(id: String, transportation: crate::models::RequestTransportation, travel_time: i32, arrival_time: String, properties: Vec<crate::models::RequestTimeFilterPostcodesProperty>) -> RequestTimeFilterPostcodesArrivalSearch {
        RequestTimeFilterPostcodesArrivalSearch {
            id,
            transportation: Box::new(transportation),
            travel_time,
            arrival_time,
            properties,
            range: None,
        }
    }
}


