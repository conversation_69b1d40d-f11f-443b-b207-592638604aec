/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseSupportedLocation {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "map_name")]
    pub map_name: String,
}

impl ResponseSupportedLocation {
    pub fn new(id: String, map_name: String) -> ResponseSupportedLocation {
        ResponseSupportedLocation {
            id,
            map_name,
        }
    }
}


