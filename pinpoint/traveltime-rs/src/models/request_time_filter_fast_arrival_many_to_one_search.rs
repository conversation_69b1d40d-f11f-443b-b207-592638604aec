/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, Debug, <PERSON>ialEq, Default, Serialize, Deserialize)]
pub struct RequestTimeFilterFastArrivalManyToOneSearch {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "arrival_location_id")]
    pub arrival_location_id: String,
    #[serde(rename = "departure_location_ids")]
    pub departure_location_ids: Vec<String>,
    #[serde(rename = "transportation")]
    pub transportation: Box<crate::models::RequestTransportationFast>,
    #[serde(rename = "travel_time")]
    pub travel_time: i32,
    #[serde(rename = "arrival_time_period")]
    pub arrival_time_period: crate::models::RequestArrivalTimePeriod,
    #[serde(rename = "properties")]
    pub properties: Vec<crate::models::RequestTimeFilterFastProperty>,
}

impl RequestTimeFilterFastArrivalManyToOneSearch {
    pub fn new(id: String, arrival_location_id: String, departure_location_ids: Vec<String>, transportation: crate::models::RequestTransportationFast, travel_time: i32, arrival_time_period: crate::models::RequestArrivalTimePeriod, properties: Vec<crate::models::RequestTimeFilterFastProperty>) -> RequestTimeFilterFastArrivalManyToOneSearch {
        RequestTimeFilterFastArrivalManyToOneSearch {
            id,
            arrival_location_id,
            departure_location_ids,
            transportation: Box::new(transportation),
            travel_time,
            arrival_time_period,
            properties,
        }
    }
}


