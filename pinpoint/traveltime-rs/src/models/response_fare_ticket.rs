/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(Clone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseFareTicket {
    #[serde(rename = "type")]
    pub r#type: RHashType,
    #[serde(rename = "price")]
    pub price: f64,
    #[serde(rename = "currency")]
    pub currency: String,
}

impl ResponseFareTicket {
    pub fn new(r#type: RHashType, price: f64, currency: String) -> ResponseFareTicket {
        ResponseFareTicket {
            r#type,
            price,
            currency,
        }
    }
}

/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RHashType {
    #[serde(rename = "single")]
    Single,
    #[serde(rename = "week")]
    Week,
    #[serde(rename = "month")]
    Month,
    #[serde(rename = "year")]
    Year,
}

impl Default for RHashType {
    fn default() -> RHashType {
        Self::Single
    }
}

