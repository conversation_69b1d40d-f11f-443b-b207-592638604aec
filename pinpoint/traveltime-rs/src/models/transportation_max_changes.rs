/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct TransportationMaxChanges {
    #[serde(rename = "enabled", skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
    #[serde(rename = "limit", skip_serializing_if = "Option::is_none")]
    pub limit: Option<i32>,
}

impl TransportationMaxChanges {
    pub fn new() -> TransportationMaxChanges {
        TransportationMaxChanges {
            enabled: None,
            limit: None,
        }
    }
}


