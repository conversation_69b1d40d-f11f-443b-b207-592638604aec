/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, <PERSON>bug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseBox {
    #[serde(rename = "min_lat")]
    pub min_lat: f64,
    #[serde(rename = "max_lat")]
    pub max_lat: f64,
    #[serde(rename = "min_lng")]
    pub min_lng: f64,
    #[serde(rename = "max_lng")]
    pub max_lng: f64,
}

impl ResponseBox {
    pub fn new(min_lat: f64, max_lat: f64, min_lng: f64, max_lng: f64) -> ResponseBox {
        ResponseBox {
            min_lat,
            max_lat,
            min_lng,
            max_lng,
        }
    }
}


