/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseTimeFilterPostcodes {
    #[serde(rename = "results")]
    pub results: Vec<crate::models::ResponseTimeFilterPostcodesResult>,
}

impl ResponseTimeFilterPostcodes {
    pub fn new(results: Vec<crate::models::ResponseTimeFilterPostcodesResult>) -> ResponseTimeFilterPostcodes {
        ResponseTimeFilterPostcodes {
            results,
        }
    }
}


