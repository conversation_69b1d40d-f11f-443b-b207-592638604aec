/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseTimeFilterResult {
    #[serde(rename = "search_id")]
    pub search_id: String,
    #[serde(rename = "locations")]
    pub locations: Vec<crate::models::ResponseTimeFilterLocation>,
    #[serde(rename = "unreachable")]
    pub unreachable: Vec<String>,
}

impl ResponseTimeFilterResult {
    pub fn new(search_id: String, locations: Vec<crate::models::ResponseTimeFilterLocation>, unreachable: Vec<String>) -> ResponseTimeFilterResult {
        ResponseTimeFilterResult {
            search_id,
            locations,
            unreachable,
        }
    }
}


