/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


/// 
#[derive(<PERSON>lone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RequestTimeFilterProperty {
    #[serde(rename = "travel_time")]
    TravelTime,
    #[serde(rename = "distance")]
    Distance,
    #[serde(rename = "distance_breakdown")]
    DistanceBreakdown,
    #[serde(rename = "fares")]
    Fares,
    #[serde(rename = "route")]
    Route,

}

impl ToString for RequestTimeFilterProperty {
    fn to_string(&self) -> String {
        match self {
            Self::TravelTime => String::from("travel_time"),
            Self::Distance => String::from("distance"),
            Self::DistanceBreakdown => String::from("distance_breakdown"),
            Self::Fares => String::from("fares"),
            Self::Route => String::from("route"),
        }
    }
}

impl Default for RequestTimeFilterProperty {
    fn default() -> RequestTimeFilterProperty {
        Self::TravelTime
    }
}




