/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum ResponseTransportationMode {
    #[serde(rename = "car")]
    Car,
    #[serde(rename = "parking")]
    Parking,
    #[serde(rename = "boarding")]
    Boarding,
    #[serde(rename = "walk")]
    Walk,
    #[serde(rename = "bike")]
    Bike,
    #[serde(rename = "train")]
    Train,
    #[serde(rename = "rail_national")]
    RailNational,
    #[serde(rename = "rail_overground")]
    RailOverground,
    #[serde(rename = "rail_underground")]
    RailUnderground,
    #[serde(rename = "rail_dlr")]
    RailDlr,
    #[serde(rename = "bus")]
    Bus,
    #[serde(rename = "cable_car")]
    CableCar,
    #[serde(rename = "plane")]
    Plane,
    #[serde(rename = "ferry")]
    Ferry,
    #[serde(rename = "coach")]
    Coach,

}

impl ToString for ResponseTransportationMode {
    fn to_string(&self) -> String {
        match self {
            Self::Car => String::from("car"),
            Self::Parking => String::from("parking"),
            Self::Boarding => String::from("boarding"),
            Self::Walk => String::from("walk"),
            Self::Bike => String::from("bike"),
            Self::Train => String::from("train"),
            Self::RailNational => String::from("rail_national"),
            Self::RailOverground => String::from("rail_overground"),
            Self::RailUnderground => String::from("rail_underground"),
            Self::RailDlr => String::from("rail_dlr"),
            Self::Bus => String::from("bus"),
            Self::CableCar => String::from("cable_car"),
            Self::Plane => String::from("plane"),
            Self::Ferry => String::from("ferry"),
            Self::Coach => String::from("coach"),
        }
    }
}

impl Default for ResponseTransportationMode {
    fn default() -> ResponseTransportationMode {
        Self::Car
    }
}




