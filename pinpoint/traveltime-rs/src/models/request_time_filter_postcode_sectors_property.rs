/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


/// 
#[derive(<PERSON>lone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RequestTimeFilterPostcodeSectorsProperty {
    #[serde(rename = "travel_time_reachable")]
    TravelTimeReachable,
    #[serde(rename = "travel_time_all")]
    TravelTimeAll,
    #[serde(rename = "coverage")]
    Coverage,

}

impl ToString for RequestTimeFilterPostcodeSectorsProperty {
    fn to_string(&self) -> String {
        match self {
            Self::TravelTimeReachable => String::from("travel_time_reachable"),
            Self::TravelTimeAll => String::from("travel_time_all"),
            Self::Coverage => String::from("coverage"),
        }
    }
}

impl Default for RequestTimeFilterPostcodeSectorsProperty {
    fn default() -> RequestTimeFilterPostcodeSectorsProperty {
        Self::TravelTimeReachable
    }
}




