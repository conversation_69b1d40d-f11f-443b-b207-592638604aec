/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, <PERSON>bug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseTimeFilterFastProperties {
    #[serde(rename = "travel_time", skip_serializing_if = "Option::is_none")]
    pub travel_time: Option<i32>,
    #[serde(rename = "fares", skip_serializing_if = "Option::is_none")]
    pub fares: Option<Box<crate::models::ResponseFaresFast>>,
}

impl ResponseTimeFilterFastProperties {
    pub fn new() -> ResponseTimeFilterFastProperties {
        ResponseTimeFilterFastProperties {
            travel_time: None,
            fares: None,
        }
    }
}


