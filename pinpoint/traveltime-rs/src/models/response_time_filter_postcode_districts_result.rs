/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, <PERSON>bug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseTimeFilterPostcodeDistrictsResult {
    #[serde(rename = "search_id")]
    pub search_id: String,
    #[serde(rename = "districts")]
    pub districts: Vec<crate::models::ResponseTimeFilterPostcodeDistrict>,
}

impl ResponseTimeFilterPostcodeDistrictsResult {
    pub fn new(search_id: String, districts: Vec<crate::models::ResponseTimeFilterPostcodeDistrict>) -> ResponseTimeFilterPostcodeDistrictsResult {
        ResponseTimeFilterPostcodeDistrictsResult {
            search_id,
            districts,
        }
    }
}


