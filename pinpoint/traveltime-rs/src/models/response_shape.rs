/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseShape {
    #[serde(rename = "shell")]
    pub shell: Vec<crate::models::Coords>,
    #[serde(rename = "holes")]
    pub holes: Vec<Vec<crate::models::Coords>>,
}

impl ResponseShape {
    pub fn new(shell: Vec<crate::models::Coords>, holes: Vec<Vec<crate::models::Coords>>) -> ResponseShape {
        ResponseShape {
            shell,
            holes,
        }
    }
}


