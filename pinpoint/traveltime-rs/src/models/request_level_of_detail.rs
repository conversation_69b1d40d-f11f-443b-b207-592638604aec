/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct RequestLevelOfDetail {
    #[serde(rename = "scale_type")]
    pub scale_type: ScaleType,
    #[serde(rename = "level")]
    pub level: Level,
}

impl RequestLevelOfDetail {
    pub fn new(scale_type: ScaleType, level: Level) -> RequestLevelOfDetail {
        RequestLevelOfDetail {
            scale_type,
            level,
        }
    }
}

/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum ScaleType {
    #[serde(rename = "simple")]
    Simple,
}

impl Default for ScaleType {
    fn default() -> ScaleType {
        Self::Simple
    }
}
/// 
#[derive(<PERSON>lone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum Level {
    #[serde(rename = "lowest")]
    Lowest,
    #[serde(rename = "low")]
    Low,
    #[serde(rename = "medium")]
    Medium,
    #[serde(rename = "high")]
    High,
    #[serde(rename = "highest")]
    Highest,
}

impl Default for Level {
    fn default() -> Level {
        Self::Lowest
    }
}

