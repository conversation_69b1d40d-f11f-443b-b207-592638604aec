/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RequestArrivalTimePeriod {
    #[serde(rename = "weekday_morning")]
    WeekdayMorning,

}

impl ToString for RequestArrivalTimePeriod {
    fn to_string(&self) -> String {
        match self {
            Self::WeekdayMorning => String::from("weekday_morning"),
        }
    }
}

impl Default for RequestArrivalTimePeriod {
    fn default() -> RequestArrivalTimePeriod {
        Self::WeekdayMorning
    }
}




