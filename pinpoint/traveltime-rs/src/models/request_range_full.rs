/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct RequestRangeFull {
    #[serde(rename = "enabled")]
    pub enabled: bool,
    #[serde(rename = "max_results")]
    pub max_results: i32,
    #[serde(rename = "width")]
    pub width: i32,
}

impl RequestRangeFull {
    pub fn new(enabled: bool, max_results: i32, width: i32) -> RequestRangeFull {
        RequestRangeFull {
            enabled,
            max_results,
            width,
        }
    }
}


