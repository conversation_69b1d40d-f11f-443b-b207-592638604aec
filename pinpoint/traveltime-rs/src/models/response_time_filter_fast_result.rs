/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseTimeFilterFastResult {
    #[serde(rename = "search_id")]
    pub search_id: String,
    #[serde(rename = "locations")]
    pub locations: Vec<crate::models::ResponseTimeFilterFastLocation>,
    #[serde(rename = "unreachable")]
    pub unreachable: Vec<String>,
}

impl ResponseTimeFilterFastResult {
    pub fn new(search_id: String, locations: Vec<crate::models::ResponseTimeFilterFastLocation>, unreachable: Vec<String>) -> ResponseTimeFilterFastResult {
        ResponseTimeFilterFastResult {
            search_id,
            locations,
            unreachable,
        }
    }
}


