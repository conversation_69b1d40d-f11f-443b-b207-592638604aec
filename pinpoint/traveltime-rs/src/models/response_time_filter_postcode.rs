/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, <PERSON>bug, <PERSON>ialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseTimeFilterPostcode {
    #[serde(rename = "code")]
    pub code: String,
    #[serde(rename = "properties")]
    pub properties: Vec<crate::models::ResponseTimeFilterPostcodesProperties>,
}

impl ResponseTimeFilterPostcode {
    pub fn new(code: String, properties: Vec<crate::models::ResponseTimeFilterPostcodesProperties>) -> ResponseTimeFilterPostcode {
        ResponseTimeFilterPostcode {
            code,
            properties,
        }
    }
}


