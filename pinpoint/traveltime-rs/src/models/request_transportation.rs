/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, Default, Serialize, Deserialize)]
pub struct RequestTransportation {
    #[serde(rename = "type")]
    pub r#type: RHashType,
    #[serde(rename = "disable_border_crossing", skip_serializing_if = "Option::is_none")]
    pub disable_border_crossing: Option<bool>,
    #[serde(rename = "pt_change_delay", skip_serializing_if = "Option::is_none")]
    pub pt_change_delay: Option<i32>,
    #[serde(rename = "walking_time", skip_serializing_if = "Option::is_none")]
    pub walking_time: Option<i32>,
    #[serde(rename = "driving_time_to_station", skip_serializing_if = "Option::is_none")]
    pub driving_time_to_station: Option<i32>,
    #[serde(rename = "cycling_time_to_station", skip_serializing_if = "Option::is_none")]
    pub cycling_time_to_station: Option<i32>,
    #[serde(rename = "parking_time", skip_serializing_if = "Option::is_none")]
    pub parking_time: Option<i32>,
    #[serde(rename = "boarding_time", skip_serializing_if = "Option::is_none")]
    pub boarding_time: Option<i32>,
    #[serde(rename = "max_changes", skip_serializing_if = "Option::is_none")]
    pub max_changes: Option<Box<crate::models::TransportationMaxChanges>>,
}

impl RequestTransportation {
    pub fn new(r#type: RHashType) -> RequestTransportation {
        RequestTransportation {
            r#type,
            disable_border_crossing: None,
            pt_change_delay: None,
            walking_time: None,
            driving_time_to_station: None,
            cycling_time_to_station: None,
            parking_time: None,
            boarding_time: None,
            max_changes: None,
        }
    }
}

/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RHashType {
    #[serde(rename = "cycling")]
    Cycling,
    #[serde(rename = "driving")]
    Driving,
    #[serde(rename = "driving+train")]
    DrivingPlusTrain,
    #[serde(rename = "public_transport")]
    PublicTransport,
    #[serde(rename = "walking")]
    Walking,
    #[serde(rename = "coach")]
    Coach,
    #[serde(rename = "bus")]
    Bus,
    #[serde(rename = "train")]
    Train,
    #[serde(rename = "ferry")]
    Ferry,
    #[serde(rename = "driving+ferry")]
    DrivingPlusFerry,
    #[serde(rename = "cycling+ferry")]
    CyclingPlusFerry,
    #[serde(rename = "cycling+public_transport")]
    CyclingPlusPublicTransport,
}

impl Default for RHashType {
    fn default() -> RHashType {
        Self::Cycling
    }
}

