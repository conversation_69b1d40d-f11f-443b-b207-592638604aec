/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(Clone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct RequestTransportationFast {
    #[serde(rename = "type")]
    pub r#type: RHashType,
}

impl RequestTransportationFast {
    pub fn new(r#type: RHashType) -> RequestTransportationFast {
        RequestTransportationFast {
            r#type,
        }
    }
}

/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RHashType {
    #[serde(rename = "public_transport")]
    PublicTransport,
    #[serde(rename = "driving")]
    Driving,
    #[serde(rename = "driving+public_transport")]
    DrivingPlusPublicTransport,
}

impl Default for RHashType {
    fn default() -> RHashType {
        Self::PublicTransport
    }
}

