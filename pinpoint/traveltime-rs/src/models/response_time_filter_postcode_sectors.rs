/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, PartialEq, De<PERSON>ult, <PERSON>ialize, Deserialize)]
pub struct ResponseTimeFilterPostcodeSectors {
    #[serde(rename = "results")]
    pub results: Vec<crate::models::ResponseTimeFilterPostcodeSectorsResult>,
}

impl ResponseTimeFilterPostcodeSectors {
    pub fn new(results: Vec<crate::models::ResponseTimeFilterPostcodeSectorsResult>) -> ResponseTimeFilterPostcodeSectors {
        ResponseTimeFilterPostcodeSectors {
            results,
        }
    }
}


