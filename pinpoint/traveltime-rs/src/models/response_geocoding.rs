/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseGeocoding {
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "features")]
    pub features: Vec<crate::models::ResponseGeocodingGeoJsonFeature>,
}

impl ResponseGeocoding {
    pub fn new(r#type: String, features: Vec<crate::models::ResponseGeocodingGeoJsonFeature>) -> ResponseGeocoding {
        ResponseGeocoding {
            r#type,
            features,
        }
    }
}


