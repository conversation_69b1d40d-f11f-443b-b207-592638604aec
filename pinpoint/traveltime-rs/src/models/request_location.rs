/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct RequestLocation {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "coords")]
    pub coords: Box<crate::models::Coords>,
}

impl RequestLocation {
    pub fn new(id: String, coords: crate::models::Coords) -> RequestLocation {
        RequestLocation {
            id,
            coords: Box::new(coords),
        }
    }
}


