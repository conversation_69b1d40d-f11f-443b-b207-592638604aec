/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, PartialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseGeocodingGeometry {
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "coordinates")]
    pub coordinates: Vec<f64>,
}

impl ResponseGeocodingGeometry {
    pub fn new(r#type: String, coordinates: Vec<f64>) -> ResponseGeocodingGeometry {
        ResponseGeocodingGeometry {
            r#type,
            coordinates,
        }
    }
}


