/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct RequestTimeFilterFast {
    #[serde(rename = "locations")]
    pub locations: Vec<crate::models::RequestLocation>,
    #[serde(rename = "arrival_searches")]
    pub arrival_searches: Box<crate::models::RequestTimeFilterFastArrivalSearches>,
}

impl RequestTimeFilterFast {
    pub fn new(locations: Vec<crate::models::RequestLocation>, arrival_searches: crate::models::RequestTimeFilterFastArrivalSearches) -> RequestTimeFilterFast {
        RequestTimeFilterFast {
            locations,
            arrival_searches: Box::new(arrival_searches),
        }
    }
}


