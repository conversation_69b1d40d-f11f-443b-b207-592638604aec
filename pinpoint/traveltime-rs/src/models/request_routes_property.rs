/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RequestRoutesProperty {
    #[serde(rename = "travel_time")]
    TravelTime,
    #[serde(rename = "distance")]
    Distance,
    #[serde(rename = "fares")]
    Fares,
    #[serde(rename = "route")]
    Route,

}

impl ToString for RequestRoutesProperty {
    fn to_string(&self) -> String {
        match self {
            Self::TravelTime => String::from("travel_time"),
            Self::Distance => String::from("distance"),
            Self::Fares => String::from("fares"),
            Self::Route => String::from("route"),
        }
    }
}

impl Default for RequestRoutesProperty {
    fn default() -> RequestRoutesProperty {
        Self::TravelTime
    }
}




