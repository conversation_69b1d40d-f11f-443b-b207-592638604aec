/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct RequestUnionOnIntersection {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "search_ids")]
    pub search_ids: Vec<String>,
}

impl RequestUnionOnIntersection {
    pub fn new(id: String, search_ids: Vec<String>) -> RequestUnionOnIntersection {
        RequestUnionOnIntersection {
            id,
            search_ids,
        }
    }
}


