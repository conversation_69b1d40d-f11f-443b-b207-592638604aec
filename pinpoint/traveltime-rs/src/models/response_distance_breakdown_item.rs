/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseDistanceBreakdownItem {
    #[serde(rename = "mode")]
    pub mode: crate::models::ResponseTransportationMode,
    #[serde(rename = "distance")]
    pub distance: i32,
}

impl ResponseDistanceBreakdownItem {
    pub fn new(mode: crate::models::ResponseTransportationMode, distance: i32) -> ResponseDistanceBreakdownItem {
        ResponseDistanceBreakdownItem {
            mode,
            distance,
        }
    }
}


