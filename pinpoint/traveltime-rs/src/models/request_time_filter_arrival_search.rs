/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, Debug, <PERSON>ialEq, Default, Serialize, Deserialize)]
pub struct RequestTimeFilterArrivalSearch {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "departure_location_ids")]
    pub departure_location_ids: Vec<String>,
    #[serde(rename = "arrival_location_id")]
    pub arrival_location_id: String,
    #[serde(rename = "transportation")]
    pub transportation: Box<crate::models::RequestTransportation>,
    #[serde(rename = "travel_time")]
    pub travel_time: i32,
    #[serde(rename = "arrival_time")]
    pub arrival_time: String,
    #[serde(rename = "properties")]
    pub properties: Vec<crate::models::RequestTimeFilterProperty>,
    #[serde(rename = "range", skip_serializing_if = "Option::is_none")]
    pub range: Option<Box<crate::models::RequestRangeFull>>,
}

impl RequestTimeFilterArrivalSearch {
    pub fn new(id: String, departure_location_ids: Vec<String>, arrival_location_id: String, transportation: crate::models::RequestTransportation, travel_time: i32, arrival_time: String, properties: Vec<crate::models::RequestTimeFilterProperty>) -> RequestTimeFilterArrivalSearch {
        RequestTimeFilterArrivalSearch {
            id,
            departure_location_ids,
            arrival_location_id,
            transportation: Box::new(transportation),
            travel_time,
            arrival_time,
            properties,
            range: None,
        }
    }
}


