/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RequestTimeFilterPostcodesProperty {
    #[serde(rename = "travel_time")]
    TravelTime,
    #[serde(rename = "distance")]
    Distance,

}

impl ToString for RequestTimeFilterPostcodesProperty {
    fn to_string(&self) -> String {
        match self {
            Self::TravelTime => String::from("travel_time"),
            Self::Distance => String::from("distance"),
        }
    }
}

impl Default for RequestTimeFilterPostcodesProperty {
    fn default() -> RequestTimeFilterPostcodesProperty {
        Self::TravelTime
    }
}




