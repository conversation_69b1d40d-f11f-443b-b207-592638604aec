/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseRoutesLocation {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "properties")]
    pub properties: Vec<crate::models::ResponseRoutesProperties>,
}

impl ResponseRoutesLocation {
    pub fn new(id: String, properties: Vec<crate::models::ResponseRoutesProperties>) -> ResponseRoutesLocation {
        ResponseRoutesLocation {
            id,
            properties,
        }
    }
}


