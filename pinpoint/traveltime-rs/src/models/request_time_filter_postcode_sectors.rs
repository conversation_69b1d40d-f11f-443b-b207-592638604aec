/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct RequestTimeFilterPostcodeSectors {
    #[serde(rename = "departure_searches", skip_serializing_if = "Option::is_none")]
    pub departure_searches: Option<Vec<crate::models::RequestTimeFilterPostcodeSectorsDepartureSearch>>,
    #[serde(rename = "arrival_searches", skip_serializing_if = "Option::is_none")]
    pub arrival_searches: Option<Vec<crate::models::RequestTimeFilterPostcodeSectorsArrivalSearch>>,
}

impl RequestTimeFilterPostcodeSectors {
    pub fn new() -> RequestTimeFilterPostcodeSectors {
        RequestTimeFilterPostcodeSectors {
            departure_searches: None,
            arrival_searches: None,
        }
    }
}


