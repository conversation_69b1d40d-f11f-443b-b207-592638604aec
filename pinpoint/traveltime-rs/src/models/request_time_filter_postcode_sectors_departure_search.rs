/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct RequestTimeFilterPostcodeSectorsDepartureSearch {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "transportation")]
    pub transportation: Box<crate::models::RequestTransportation>,
    #[serde(rename = "travel_time")]
    pub travel_time: i32,
    #[serde(rename = "departure_time")]
    pub departure_time: String,
    #[serde(rename = "reachable_postcodes_threshold")]
    pub reachable_postcodes_threshold: f64,
    #[serde(rename = "properties")]
    pub properties: Vec<crate::models::RequestTimeFilterPostcodeSectorsProperty>,
    #[serde(rename = "range", skip_serializing_if = "Option::is_none")]
    pub range: Option<Box<crate::models::RequestRangeFull>>,
}

impl RequestTimeFilterPostcodeSectorsDepartureSearch {
    pub fn new(id: String, transportation: crate::models::RequestTransportation, travel_time: i32, departure_time: String, reachable_postcodes_threshold: f64, properties: Vec<crate::models::RequestTimeFilterPostcodeSectorsProperty>) -> RequestTimeFilterPostcodeSectorsDepartureSearch {
        RequestTimeFilterPostcodeSectorsDepartureSearch {
            id,
            transportation: Box::new(transportation),
            travel_time,
            departure_time,
            reachable_postcodes_threshold,
            properties,
            range: None,
        }
    }
}


