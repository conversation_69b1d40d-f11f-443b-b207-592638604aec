/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, Default, Serialize, Deserialize)]
pub struct ResponseTimeMapBoundingBoxesResult {
    #[serde(rename = "search_id")]
    pub search_id: String,
    #[serde(rename = "bounding_boxes")]
    pub bounding_boxes: Vec<crate::models::ResponseBoundingBox>,
    #[serde(rename = "properties")]
    pub properties: Box<crate::models::ResponseTimeMapProperties>,
}

impl ResponseTimeMapBoundingBoxesResult {
    pub fn new(search_id: String, bounding_boxes: Vec<crate::models::ResponseBoundingBox>, properties: crate::models::ResponseTimeMapProperties) -> ResponseTimeMapBoundingBoxesResult {
        ResponseTimeMapBoundingBoxesResult {
            search_id,
            bounding_boxes,
            properties: Box::new(properties),
        }
    }
}


