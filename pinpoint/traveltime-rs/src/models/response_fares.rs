/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseFares {
    #[serde(rename = "breakdown")]
    pub breakdown: Vec<crate::models::ResponseFaresBreakdownItem>,
    #[serde(rename = "tickets_total")]
    pub tickets_total: Vec<crate::models::ResponseFareTicket>,
}

impl ResponseFares {
    pub fn new(breakdown: Vec<crate::models::ResponseFaresBreakdownItem>, tickets_total: Vec<crate::models::ResponseFareTicket>) -> ResponseFares {
        ResponseFares {
            breakdown,
            tickets_total,
        }
    }
}


