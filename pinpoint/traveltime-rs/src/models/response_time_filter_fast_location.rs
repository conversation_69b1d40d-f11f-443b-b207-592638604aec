/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, De<PERSON>ult, Serialize, Deserialize)]
pub struct ResponseTimeFilterFastLocation {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "properties")]
    pub properties: Vec<crate::models::ResponseTimeFilterFastProperties>,
}

impl ResponseTimeFilterFastLocation {
    pub fn new(id: String, properties: Vec<crate::models::ResponseTimeFilterFastProperties>) -> ResponseTimeFilterFastLocation {
        ResponseTimeFilterFastLocation {
            id,
            properties,
        }
    }
}


