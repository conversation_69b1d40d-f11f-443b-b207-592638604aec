/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>ialEq, Default, Serialize, Deserialize)]
pub struct ResponseFaresBreakdownItem {
    #[serde(rename = "modes")]
    pub modes: Vec<crate::models::ResponseTransportationMode>,
    #[serde(rename = "route_part_ids")]
    pub route_part_ids: Vec<i32>,
    #[serde(rename = "tickets")]
    pub tickets: Vec<crate::models::ResponseFareTicket>,
}

impl ResponseFaresBreakdownItem {
    pub fn new(modes: Vec<crate::models::ResponseTransportationMode>, route_part_ids: Vec<i32>, tickets: Vec<crate::models::ResponseFareTicket>) -> ResponseFaresBreakdownItem {
        ResponseFaresBreakdownItem {
            modes,
            route_part_ids,
            tickets,
        }
    }
}


