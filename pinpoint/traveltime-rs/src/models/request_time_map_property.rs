/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


/// 
#[derive(<PERSON>lone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RequestTimeMapProperty {
    #[serde(rename = "is_only_walking")]
    IsOnlyWalking,

}

impl ToString for RequestTimeMapProperty {
    fn to_string(&self) -> String {
        match self {
            Self::IsOnlyWalking => String::from("is_only_walking"),
        }
    }
}

impl Default for RequestTimeMapProperty {
    fn default() -> RequestTimeMapProperty {
        Self::IsOnlyWalking
    }
}




