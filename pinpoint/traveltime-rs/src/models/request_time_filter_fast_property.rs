/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */


/// 
#[derive(Clone, Copy, Debug, Eq, PartialEq, Ord, PartialOrd, Hash, Serialize, Deserialize)]
pub enum RequestTimeFilterFastProperty {
    #[serde(rename = "travel_time")]
    TravelTime,
    #[serde(rename = "fares")]
    Fares,

}

impl ToString for RequestTimeFilterFastProperty {
    fn to_string(&self) -> String {
        match self {
            Self::TravelTime => String::from("travel_time"),
            Self::Fares => String::from("fares"),
        }
    }
}

impl Default for RequestTimeFilterFastProperty {
    fn default() -> RequestTimeFilterFastProperty {
        Self::TravelTime
    }
}




