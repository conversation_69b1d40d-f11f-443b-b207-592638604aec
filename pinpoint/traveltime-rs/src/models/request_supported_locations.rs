/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct RequestSupportedLocations {
    #[serde(rename = "locations")]
    pub locations: Vec<crate::models::RequestLocation>,
}

impl RequestSupportedLocations {
    pub fn new(locations: Vec<crate::models::RequestLocation>) -> RequestSupportedLocations {
        RequestSupportedLocations {
            locations,
        }
    }
}


