/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, <PERSON>bug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseSupportedLocations {
    #[serde(rename = "locations")]
    pub locations: Vec<crate::models::ResponseSupportedLocation>,
    #[serde(rename = "unsupported_locations")]
    pub unsupported_locations: Vec<String>,
}

impl ResponseSupportedLocations {
    pub fn new(locations: Vec<crate::models::ResponseSupportedLocation>, unsupported_locations: Vec<String>) -> ResponseSupportedLocations {
        ResponseSupportedLocations {
            locations,
            unsupported_locations,
        }
    }
}


