/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, <PERSON>bug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseTimeMapWktResult {
    #[serde(rename = "search_id")]
    pub search_id: String,
    #[serde(rename = "shape")]
    pub shape: String,
    #[serde(rename = "properties")]
    pub properties: Box<crate::models::ResponseTimeMapProperties>,
}

impl ResponseTimeMapWktResult {
    pub fn new(search_id: String, shape: String, properties: crate::models::ResponseTimeMapProperties) -> ResponseTimeMapWktResult {
        ResponseTimeMapWktResult {
            search_id,
            shape,
            properties: Box::new(properties),
        }
    }
}


