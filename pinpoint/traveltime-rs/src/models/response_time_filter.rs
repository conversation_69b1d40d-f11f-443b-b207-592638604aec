/*
 * TravelTime API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.3
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 */




#[derive(<PERSON>lone, Debug, PartialEq, Default, Serialize, Deserialize)]
pub struct ResponseTimeFilter {
    #[serde(rename = "results")]
    pub results: Vec<crate::models::ResponseTimeFilterResult>,
}

impl ResponseTimeFilter {
    pub fn new(results: Vec<crate::models::ResponseTimeFilterResult>) -> ResponseTimeFilter {
        ResponseTimeFilter {
            results,
        }
    }
}


