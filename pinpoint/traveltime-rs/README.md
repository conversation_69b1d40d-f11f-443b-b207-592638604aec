# Rust API client for openapi

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)


## Overview

This API client was generated by the [OpenAPI Generator](https://openapi-generator.tech) project.  By using the [openapi-spec](https://openapis.org) from a remote server, you can easily generate an API client.

- API version: 1.2.3
- Package version: 1.2.3
- Build package: `org.openapitools.codegen.languages.RustClientCodegen`

## Installation

Put the package under your project folder in a directory named `openapi` and add the following to `Cargo.toml` under `[dependencies]`:

```
openapi = { path = "./openapi" }
```

## Documentation for API Endpoints

All URIs are relative to *https://1k8cheq5kc.execute-api.eu-west-2.amazonaws.com*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*DefaultApi* | [**geocoding_reverse_search**](docs/DefaultApi.md#geocoding_reverse_search) | **GET** /geocoding/reverse | 
*DefaultApi* | [**geocoding_search**](docs/DefaultApi.md#geocoding_search) | **GET** /geocoding/search | 
*DefaultApi* | [**map_info**](docs/DefaultApi.md#map_info) | **GET** /map-info | 
*DefaultApi* | [**routes**](docs/DefaultApi.md#routes) | **POST** /routes | 
*DefaultApi* | [**supported_locations**](docs/DefaultApi.md#supported_locations) | **POST** /supported-locations | 
*DefaultApi* | [**time_filter**](docs/DefaultApi.md#time_filter) | **POST** /time-filter | 
*DefaultApi* | [**time_filter_fast**](docs/DefaultApi.md#time_filter_fast) | **POST** /time-filter/fast | 
*DefaultApi* | [**time_filter_postcode_districts**](docs/DefaultApi.md#time_filter_postcode_districts) | **POST** /time-filter/postcode-districts | 
*DefaultApi* | [**time_filter_postcode_sectors**](docs/DefaultApi.md#time_filter_postcode_sectors) | **POST** /time-filter/postcode-sectors | 
*DefaultApi* | [**time_filter_postcodes**](docs/DefaultApi.md#time_filter_postcodes) | **POST** /time-filter/postcodes | 
*DefaultApi* | [**time_map**](docs/DefaultApi.md#time_map) | **POST** /time-map | 


## Documentation For Models

 - [Coords](docs/Coords.md)
 - [RequestArrivalTimePeriod](docs/RequestArrivalTimePeriod.md)
 - [RequestLevelOfDetail](docs/RequestLevelOfDetail.md)
 - [RequestLocation](docs/RequestLocation.md)
 - [RequestRangeFull](docs/RequestRangeFull.md)
 - [RequestRangeNoMaxResults](docs/RequestRangeNoMaxResults.md)
 - [RequestRoutes](docs/RequestRoutes.md)
 - [RequestRoutesArrivalSearch](docs/RequestRoutesArrivalSearch.md)
 - [RequestRoutesDepartureSearch](docs/RequestRoutesDepartureSearch.md)
 - [RequestRoutesProperty](docs/RequestRoutesProperty.md)
 - [RequestSupportedLocations](docs/RequestSupportedLocations.md)
 - [RequestTimeFilter](docs/RequestTimeFilter.md)
 - [RequestTimeFilterArrivalSearch](docs/RequestTimeFilterArrivalSearch.md)
 - [RequestTimeFilterDepartureSearch](docs/RequestTimeFilterDepartureSearch.md)
 - [RequestTimeFilterFast](docs/RequestTimeFilterFast.md)
 - [RequestTimeFilterFastArrivalManyToOneSearch](docs/RequestTimeFilterFastArrivalManyToOneSearch.md)
 - [RequestTimeFilterFastArrivalOneToManySearch](docs/RequestTimeFilterFastArrivalOneToManySearch.md)
 - [RequestTimeFilterFastArrivalSearches](docs/RequestTimeFilterFastArrivalSearches.md)
 - [RequestTimeFilterFastProperty](docs/RequestTimeFilterFastProperty.md)
 - [RequestTimeFilterPostcodeDistricts](docs/RequestTimeFilterPostcodeDistricts.md)
 - [RequestTimeFilterPostcodeDistrictsArrivalSearch](docs/RequestTimeFilterPostcodeDistrictsArrivalSearch.md)
 - [RequestTimeFilterPostcodeDistrictsDepartureSearch](docs/RequestTimeFilterPostcodeDistrictsDepartureSearch.md)
 - [RequestTimeFilterPostcodeDistrictsProperty](docs/RequestTimeFilterPostcodeDistrictsProperty.md)
 - [RequestTimeFilterPostcodeSectors](docs/RequestTimeFilterPostcodeSectors.md)
 - [RequestTimeFilterPostcodeSectorsArrivalSearch](docs/RequestTimeFilterPostcodeSectorsArrivalSearch.md)
 - [RequestTimeFilterPostcodeSectorsDepartureSearch](docs/RequestTimeFilterPostcodeSectorsDepartureSearch.md)
 - [RequestTimeFilterPostcodeSectorsProperty](docs/RequestTimeFilterPostcodeSectorsProperty.md)
 - [RequestTimeFilterPostcodes](docs/RequestTimeFilterPostcodes.md)
 - [RequestTimeFilterPostcodesArrivalSearch](docs/RequestTimeFilterPostcodesArrivalSearch.md)
 - [RequestTimeFilterPostcodesDepartureSearch](docs/RequestTimeFilterPostcodesDepartureSearch.md)
 - [RequestTimeFilterPostcodesProperty](docs/RequestTimeFilterPostcodesProperty.md)
 - [RequestTimeFilterProperty](docs/RequestTimeFilterProperty.md)
 - [RequestTimeMap](docs/RequestTimeMap.md)
 - [RequestTimeMapArrivalSearch](docs/RequestTimeMapArrivalSearch.md)
 - [RequestTimeMapDepartureSearch](docs/RequestTimeMapDepartureSearch.md)
 - [RequestTimeMapProperty](docs/RequestTimeMapProperty.md)
 - [RequestTransportation](docs/RequestTransportation.md)
 - [RequestTransportationFast](docs/RequestTransportationFast.md)
 - [RequestUnionOnIntersection](docs/RequestUnionOnIntersection.md)
 - [ResponseBoundingBox](docs/ResponseBoundingBox.md)
 - [ResponseBox](docs/ResponseBox.md)
 - [ResponseDistanceBreakdownItem](docs/ResponseDistanceBreakdownItem.md)
 - [ResponseError](docs/ResponseError.md)
 - [ResponseFareTicket](docs/ResponseFareTicket.md)
 - [ResponseFares](docs/ResponseFares.md)
 - [ResponseFaresBreakdownItem](docs/ResponseFaresBreakdownItem.md)
 - [ResponseFaresFast](docs/ResponseFaresFast.md)
 - [ResponseGeocoding](docs/ResponseGeocoding.md)
 - [ResponseGeocodingGeoJsonFeature](docs/ResponseGeocodingGeoJsonFeature.md)
 - [ResponseGeocodingGeometry](docs/ResponseGeocodingGeometry.md)
 - [ResponseGeocodingProperties](docs/ResponseGeocodingProperties.md)
 - [ResponseMapInfo](docs/ResponseMapInfo.md)
 - [ResponseMapInfoFeatures](docs/ResponseMapInfoFeatures.md)
 - [ResponseMapInfoFeaturesPublicTransport](docs/ResponseMapInfoFeaturesPublicTransport.md)
 - [ResponseMapInfoMap](docs/ResponseMapInfoMap.md)
 - [ResponseRoute](docs/ResponseRoute.md)
 - [ResponseRoutePart](docs/ResponseRoutePart.md)
 - [ResponseRoutes](docs/ResponseRoutes.md)
 - [ResponseRoutesLocation](docs/ResponseRoutesLocation.md)
 - [ResponseRoutesProperties](docs/ResponseRoutesProperties.md)
 - [ResponseRoutesResult](docs/ResponseRoutesResult.md)
 - [ResponseShape](docs/ResponseShape.md)
 - [ResponseSupportedLocation](docs/ResponseSupportedLocation.md)
 - [ResponseSupportedLocations](docs/ResponseSupportedLocations.md)
 - [ResponseTimeFilter](docs/ResponseTimeFilter.md)
 - [ResponseTimeFilterFast](docs/ResponseTimeFilterFast.md)
 - [ResponseTimeFilterFastLocation](docs/ResponseTimeFilterFastLocation.md)
 - [ResponseTimeFilterFastProperties](docs/ResponseTimeFilterFastProperties.md)
 - [ResponseTimeFilterFastResult](docs/ResponseTimeFilterFastResult.md)
 - [ResponseTimeFilterLocation](docs/ResponseTimeFilterLocation.md)
 - [ResponseTimeFilterPostcode](docs/ResponseTimeFilterPostcode.md)
 - [ResponseTimeFilterPostcodeDistrict](docs/ResponseTimeFilterPostcodeDistrict.md)
 - [ResponseTimeFilterPostcodeDistrictProperties](docs/ResponseTimeFilterPostcodeDistrictProperties.md)
 - [ResponseTimeFilterPostcodeDistricts](docs/ResponseTimeFilterPostcodeDistricts.md)
 - [ResponseTimeFilterPostcodeDistrictsResult](docs/ResponseTimeFilterPostcodeDistrictsResult.md)
 - [ResponseTimeFilterPostcodeSector](docs/ResponseTimeFilterPostcodeSector.md)
 - [ResponseTimeFilterPostcodeSectorProperties](docs/ResponseTimeFilterPostcodeSectorProperties.md)
 - [ResponseTimeFilterPostcodeSectors](docs/ResponseTimeFilterPostcodeSectors.md)
 - [ResponseTimeFilterPostcodeSectorsResult](docs/ResponseTimeFilterPostcodeSectorsResult.md)
 - [ResponseTimeFilterPostcodes](docs/ResponseTimeFilterPostcodes.md)
 - [ResponseTimeFilterPostcodesProperties](docs/ResponseTimeFilterPostcodesProperties.md)
 - [ResponseTimeFilterPostcodesResult](docs/ResponseTimeFilterPostcodesResult.md)
 - [ResponseTimeFilterProperties](docs/ResponseTimeFilterProperties.md)
 - [ResponseTimeFilterResult](docs/ResponseTimeFilterResult.md)
 - [ResponseTimeMap](docs/ResponseTimeMap.md)
 - [ResponseTimeMapBoundingBoxes](docs/ResponseTimeMapBoundingBoxes.md)
 - [ResponseTimeMapBoundingBoxesResult](docs/ResponseTimeMapBoundingBoxesResult.md)
 - [ResponseTimeMapProperties](docs/ResponseTimeMapProperties.md)
 - [ResponseTimeMapResult](docs/ResponseTimeMapResult.md)
 - [ResponseTimeMapWkt](docs/ResponseTimeMapWkt.md)
 - [ResponseTimeMapWktResult](docs/ResponseTimeMapWktResult.md)
 - [ResponseTransportationMode](docs/ResponseTransportationMode.md)
 - [ResponseTravelTimeStatistics](docs/ResponseTravelTimeStatistics.md)
 - [TransportationMaxChanges](docs/TransportationMaxChanges.md)


To get access to the crate's generated documentation, use:

```
cargo doc --open
```

## Author

<EMAIL>

