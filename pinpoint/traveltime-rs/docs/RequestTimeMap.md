# RequestTimeMap

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**departure_searches** | Option<[**Vec<crate::models::RequestTimeMapDepartureSearch>**](RequestTimeMapDepartureSearch.md)> |  | [optional]
**arrival_searches** | Option<[**Vec<crate::models::RequestTimeMapArrivalSearch>**](RequestTimeMapArrivalSearch.md)> |  | [optional]
**unions** | Option<[**Vec<crate::models::RequestUnionOnIntersection>**](RequestUnionOnIntersection.md)> |  | [optional]
**intersections** | Option<[**Vec<crate::models::RequestUnionOnIntersection>**](RequestUnionOnIntersection.md)> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


