# RequestTimeFilter

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**locations** | [**Vec<crate::models::RequestLocation>**](RequestLocation.md) |  | 
**departure_searches** | Option<[**Vec<crate::models::RequestTimeFilterDepartureSearch>**](RequestTimeFilterDepartureSearch.md)> |  | [optional]
**arrival_searches** | Option<[**Vec<crate::models::RequestTimeFilterArrivalSearch>**](RequestTimeFilterArrivalSearch.md)> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


