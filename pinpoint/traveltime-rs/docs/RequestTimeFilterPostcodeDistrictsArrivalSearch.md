# RequestTimeFilterPostcodeDistrictsArrivalSearch

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** |  | 
**transportation** | [**crate::models::RequestTransportation**](RequestTransportation.md) |  | 
**travel_time** | **i32** |  | 
**arrival_time** | **String** |  | 
**reachable_postcodes_threshold** | **f64** |  | 
**properties** | [**Vec<crate::models::RequestTimeFilterPostcodeDistrictsProperty>**](RequestTimeFilterPostcodeDistrictsProperty.md) |  | 
**range** | Option<[**crate::models::RequestRangeFull**](RequestRangeFull.md)> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


