# RequestTimeFilterDepartureSearch

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** |  | 
**departure_location_id** | **String** |  | 
**arrival_location_ids** | **Vec<String>** |  | 
**transportation** | [**crate::models::RequestTransportation**](RequestTransportation.md) |  | 
**travel_time** | **i32** |  | 
**departure_time** | **String** |  | 
**properties** | [**Vec<crate::models::RequestTimeFilterProperty>**](RequestTimeFilterProperty.md) |  | 
**range** | Option<[**crate::models::RequestRangeFull**](RequestRangeFull.md)> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


