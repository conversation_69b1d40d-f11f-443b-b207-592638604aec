# RequestTransportation

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**r#type** | **String** |  | 
**disable_border_crossing** | Option<**bool**> |  | [optional]
**pt_change_delay** | Option<**i32**> |  | [optional]
**walking_time** | Option<**i32**> |  | [optional]
**driving_time_to_station** | Option<**i32**> |  | [optional]
**cycling_time_to_station** | Option<**i32**> |  | [optional]
**parking_time** | Option<**i32**> |  | [optional]
**boarding_time** | Option<**i32**> |  | [optional]
**max_changes** | Option<[**crate::models::TransportationMaxChanges**](TransportationMaxChanges.md)> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


