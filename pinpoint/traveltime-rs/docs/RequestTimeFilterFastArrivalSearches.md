# RequestTimeFilterFastArrivalSearches

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**many_to_one** | Option<[**Vec<crate::models::RequestTimeFilterFastArrivalManyToOneSearch>**](RequestTimeFilterFastArrivalManyToOneSearch.md)> |  | [optional]
**one_to_many** | Option<[**Vec<crate::models::RequestTimeFilterFastArrivalOneToManySearch>**](RequestTimeFilterFastArrivalOneToManySearch.md)> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


