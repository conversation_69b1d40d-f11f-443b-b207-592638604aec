# RequestTimeFilterFastArrivalManyToOneSearch

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** |  | 
**arrival_location_id** | **String** |  | 
**departure_location_ids** | **Vec<String>** |  | 
**transportation** | [**crate::models::RequestTransportationFast**](RequestTransportationFast.md) |  | 
**travel_time** | **i32** |  | 
**arrival_time_period** | [**crate::models::RequestArrivalTimePeriod**](RequestArrivalTimePeriod.md) |  | 
**properties** | [**Vec<crate::models::RequestTimeFilterFastProperty>**](RequestTimeFilterFastProperty.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


