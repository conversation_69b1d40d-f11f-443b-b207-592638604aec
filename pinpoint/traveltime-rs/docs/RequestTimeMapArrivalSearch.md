# RequestTimeMapArrivalSearch

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** |  | 
**coords** | [**crate::models::Coords**](Coords.md) |  | 
**transportation** | [**crate::models::RequestTransportation**](RequestTransportation.md) |  | 
**travel_time** | **i32** |  | 
**arrival_time** | **String** |  | 
**properties** | Option<[**Vec<crate::models::RequestTimeMapProperty>**](RequestTimeMapProperty.md)> |  | [optional]
**range** | Option<[**crate::models::RequestRangeNoMaxResults**](RequestRangeNoMaxResults.md)> |  | [optional]
**level_of_detail** | Option<[**crate::models::RequestLevelOfDetail**](RequestLevelOfDetail.md)> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


