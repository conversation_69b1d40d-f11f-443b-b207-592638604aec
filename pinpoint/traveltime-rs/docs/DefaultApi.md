# \DefaultApi

All URIs are relative to *https://1k8cheq5kc.execute-api.eu-west-2.amazonaws.com*

Method | HTTP request | Description
------------- | ------------- | -------------
[**geocoding_reverse_search**](DefaultApi.md#geocoding_reverse_search) | **GET** /geocoding/reverse | 
[**geocoding_search**](DefaultApi.md#geocoding_search) | **GET** /geocoding/search | 
[**map_info**](DefaultApi.md#map_info) | **GET** /map-info | 
[**routes**](DefaultApi.md#routes) | **POST** /routes | 
[**supported_locations**](DefaultApi.md#supported_locations) | **POST** /supported-locations | 
[**time_filter**](DefaultApi.md#time_filter) | **POST** /time-filter | 
[**time_filter_fast**](DefaultApi.md#time_filter_fast) | **POST** /time-filter/fast | 
[**time_filter_postcode_districts**](DefaultApi.md#time_filter_postcode_districts) | **POST** /time-filter/postcode-districts | 
[**time_filter_postcode_sectors**](DefaultApi.md#time_filter_postcode_sectors) | **POST** /time-filter/postcode-sectors | 
[**time_filter_postcodes**](DefaultApi.md#time_filter_postcodes) | **POST** /time-filter/postcodes | 
[**time_map**](DefaultApi.md#time_map) | **POST** /time-map | 



## geocoding_reverse_search

> crate::models::ResponseGeocoding geocoding_reverse_search(lat, lng, within_period_country)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**lat** | **f64** |  | [required] |
**lng** | **f64** |  | [required] |
**within_period_country** | Option<**String**> |  |  |

### Return type

[**crate::models::ResponseGeocoding**](ResponseGeocoding.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## geocoding_search

> crate::models::ResponseGeocoding geocoding_search(query, focus_period_lat, focus_period_lng, within_period_country)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**query** | **String** |  | [required] |
**focus_period_lat** | Option<**f64**> |  |  |
**focus_period_lng** | Option<**f64**> |  |  |
**within_period_country** | Option<**String**> |  |  |

### Return type

[**crate::models::ResponseGeocoding**](ResponseGeocoding.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## map_info

> crate::models::ResponseMapInfo map_info()


### Parameters

This endpoint does not need any parameter.

### Return type

[**crate::models::ResponseMapInfo**](ResponseMapInfo.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## routes

> crate::models::ResponseRoutes routes(request_routes)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**request_routes** | [**RequestRoutes**](RequestRoutes.md) |  | [required] |

### Return type

[**crate::models::ResponseRoutes**](ResponseRoutes.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## supported_locations

> crate::models::ResponseSupportedLocations supported_locations(request_supported_locations)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**request_supported_locations** | [**RequestSupportedLocations**](RequestSupportedLocations.md) |  | [required] |

### Return type

[**crate::models::ResponseSupportedLocations**](ResponseSupportedLocations.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## time_filter

> crate::models::ResponseTimeFilter time_filter(request_time_filter)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**request_time_filter** | [**RequestTimeFilter**](RequestTimeFilter.md) |  | [required] |

### Return type

[**crate::models::ResponseTimeFilter**](ResponseTimeFilter.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## time_filter_fast

> crate::models::ResponseTimeFilterFast time_filter_fast(request_time_filter_fast)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**request_time_filter_fast** | [**RequestTimeFilterFast**](RequestTimeFilterFast.md) |  | [required] |

### Return type

[**crate::models::ResponseTimeFilterFast**](ResponseTimeFilterFast.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## time_filter_postcode_districts

> crate::models::ResponseTimeFilterPostcodeDistricts time_filter_postcode_districts(request_time_filter_postcode_districts)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**request_time_filter_postcode_districts** | [**RequestTimeFilterPostcodeDistricts**](RequestTimeFilterPostcodeDistricts.md) |  | [required] |

### Return type

[**crate::models::ResponseTimeFilterPostcodeDistricts**](ResponseTimeFilterPostcodeDistricts.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## time_filter_postcode_sectors

> crate::models::ResponseTimeFilterPostcodeSectors time_filter_postcode_sectors(request_time_filter_postcode_sectors)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**request_time_filter_postcode_sectors** | [**RequestTimeFilterPostcodeSectors**](RequestTimeFilterPostcodeSectors.md) |  | [required] |

### Return type

[**crate::models::ResponseTimeFilterPostcodeSectors**](ResponseTimeFilterPostcodeSectors.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## time_filter_postcodes

> crate::models::ResponseTimeFilterPostcodes time_filter_postcodes(request_time_filter_postcodes)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**request_time_filter_postcodes** | [**RequestTimeFilterPostcodes**](RequestTimeFilterPostcodes.md) |  | [required] |

### Return type

[**crate::models::ResponseTimeFilterPostcodes**](ResponseTimeFilterPostcodes.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


## time_map

> crate::models::ResponseTimeMap time_map(request_time_map)


### Parameters


Name | Type | Description  | Required | Notes
------------- | ------------- | ------------- | ------------- | -------------
**request_time_map** | [**RequestTimeMap**](RequestTimeMap.md) |  | [required] |

### Return type

[**crate::models::ResponseTimeMap**](ResponseTimeMap.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json, application/vnd.wkt+json, application/vnd.wkt-no-holes+json, application/vnd.bounding-boxes+json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

