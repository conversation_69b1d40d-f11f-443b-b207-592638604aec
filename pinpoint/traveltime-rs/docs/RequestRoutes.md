# RequestRoutes

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**locations** | [**Vec<crate::models::RequestLocation>**](RequestLocation.md) |  | 
**departure_searches** | Option<[**Vec<crate::models::RequestRoutesDepartureSearch>**](RequestRoutesDepartureSearch.md)> |  | [optional]
**arrival_searches** | Option<[**Vec<crate::models::RequestRoutesArrivalSearch>**](RequestRoutesArrivalSearch.md)> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


