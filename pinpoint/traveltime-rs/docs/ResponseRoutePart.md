# ResponseRoutePart

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **String** |  | 
**r#type** | **String** |  | 
**mode** | [**crate::models::ResponseTransportationMode**](ResponseTransportationMode.md) |  | 
**directions** | **String** |  | 
**distance** | **i32** |  | 
**travel_time** | **i32** |  | 
**coords** | [**Vec<crate::models::Coords>**](Coords.md) |  | 
**direction** | Option<**String**> |  | [optional]
**road** | Option<**String**> |  | [optional]
**turn** | Option<**String**> |  | [optional]
**line** | Option<**String**> |  | [optional]
**departure_station** | Option<**String**> |  | [optional]
**arrival_station** | Option<**String**> |  | [optional]
**departs_at** | Option<**String**> |  | [optional]
**arrives_at** | Option<**String**> |  | [optional]
**num_stops** | Option<**i32**> |  | [optional]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


