[package]
name = "traveltime"
version = "1.2.3"
authors = ["<EMAIL>"]
description = "No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)"
# Override this license by providing a License Object in the OpenAPI.
license = "Unlicense"
edition = "2018"

[dependencies]
serde = "^1.0"
serde_derive = "^1.0"
serde_json = "^1.0"
url = "^2.2"
uuid = { version = "^1.0", features = ["serde"] }
leaflet = "0.2"
hyper = "0.14"
[dependencies.reqwest]
version = "^0.11"
features = ["json", "multipart"]

