use crate::{
    geo::{destination, Coord},
    osm::OsmNode,
    Model,
};
use ::web_sys::{Element, HtmlAnchorElement};
use gloo_events::EventListener;
use js_sys::{Array, Function};
use leaflet::{
    Circle, Control, LatLng, LatLngBounds, LayerGroup, Map, Marker, Polyline, Rectangle, TileLayer,
};
use seed::{prelude::*, window};
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
#[allow(non_snake_case)]
struct LineOptions {
    color: String,
    weight: u32,
    fillOpacity: f64,
}

#[derive(Serialize, Deserialize)]
struct CircleOptions {
    radius: f64,
}

#[derive(Serialize, Deserialize)]
struct MarkerOptions {
    title: String,
}

#[derive(Serialize, Deserialize)]
struct PopupOptions {}

#[derive(Serialize, Deserialize)]
struct ControlOptions {
    position: String,
}

#[derive(Serialize, Deserialize)]
struct ControlProps {
    options: ControlOptions,
}

pub fn init<T, U>(
    track_position_callback: T,
    wake_lock_callback: Option<U>,
) -> (Map, LayerGroup, LayerGroup, LayerGroup)
where
    T: Fn() + 'static + Clone,
    U: Fn() + 'static + Clone,
{
    let map = Map::new("map", &JsValue::NULL);

    let topology_layer_group = LayerGroup::new();
    topology_layer_group.addTo(&map);

    let position_layer_group = LayerGroup::new();
    position_layer_group.addTo(&map);

    let notes_layer_group = LayerGroup::new();
    notes_layer_group.addTo(&map);

    add_track_position_control(&map, track_position_callback);

    if let Some(callback) = wake_lock_callback {
        add_wake_lock_control(&map, callback);
    }

    TileLayer::new(
        "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
        &JsValue::NULL,
    )
    .addTo(&map);

    (
        map,
        topology_layer_group,
        position_layer_group,
        notes_layer_group,
    )
}

pub fn set_view(model: &Model) {
    if let (Some(map), position) = (&model.map, model.position) {
        map.setView(&position.into(), 19.0);
    }
}

pub fn pan_to_position(model: &Model, position: Coord) {
    if let Some(map) = &model.map {
        map.panTo(&position.into());
    }
}

pub fn render_topology_and_position(model: &Model) {
    if let (Some(topology_layer_group), Some(chunk_position)) =
        (&model.topology_layer_group, &model.osm_chunk_position)
    {
        topology_layer_group.clearLayers();

        topology_layer_group.addLayer(&Rectangle::new_with_options(
            &bbox(chunk_position, model.osm_chunk_radius),
            &JsValue::from_serde(&LineOptions {
                color: "red".into(),
                weight: 2,
                fillOpacity: 0.0,
            })
            .expect("Unable to serialize rectangle options"),
        ));

        topology_layer_group.addLayer(&Rectangle::new_with_options(
            &bbox(
                chunk_position,
                model.osm_chunk_radius * model.osm_chunk_trigger_factor,
            ),
            &JsValue::from_serde(&LineOptions {
                color: "orange".into(),
                weight: 2,
                fillOpacity: 0.0,
            })
            .expect("Unable to serialize rectangle options"),
        ));

        for way in model.osm.ways.iter() {
            topology_layer_group.addLayer(&Polyline::new_with_options(
                way.points(&model.osm)
                    .into_iter()
                    .map(LatLng::from)
                    .map(JsValue::from)
                    .collect(),
                &JsValue::from_serde(&LineOptions {
                    color: "green".into(),
                    weight: 3,
                    fillOpacity: 0.0,
                })
                .expect("Unable to serialize polyline options"),
            ));
        }
    }

    render_position(model);
}


// fn bbox(position: &Coord, radius: f64) -> LatLngBounds {
//     let north = destination(position, 0.0, radius);
//     let east = destination(position, 90.0, radius);
//     let west = destination(position, 270.0, radius);
//     let south = destination(position, 180.0, radius);

//     LatLngBounds::new(
//         &LatLng::new(south.lat, west.lon),
//         &LatLng::new(north.lat, east.lon),
//     )
// }


impl From<Coord> for LatLng {
    fn from(coord: Coord) -> Self {
        LatLng::new(coord.lat, coord.lon)
    }
}

impl From<&OsmNode> for LatLng {
    fn from(node: &OsmNode) -> Self {
        LatLng::new(node.lat, node.lon)
    }
}