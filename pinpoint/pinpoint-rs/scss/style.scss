@import "normalize";

html,
body {
  box-sizing: border-box;
}

body {
  // those styles are irrelevant, just here for nicer presentation
  display: flex;
  flex-direction: column;
  height: 400px;
  width: 800px;
  margin: 2em auto;

  .map {
    height: 400px;
  }

  .component-container {
    border-radius: 0.5em;
    padding: 1em;
  }

  .map-container {
    border: 1px solid rgb(25, 110, 68);
    margin-bottom: 1em;
  }

  .control {
    text-align: center;
    border: 1px solid rgb(25, 110, 68);

    button {
      margin: 0.5em;
    }
  }
}
