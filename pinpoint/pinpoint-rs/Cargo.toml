[package]
name = "pinpoint-rs"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
traveltime = { version = "1.2.3", path = "../traveltime-rs" }
yew = "0.19"
leaflet = "0.2"
wasm-bindgen = "0.2"
gloo-console = "0.2.3"
gloo-utils = "0.1"
serde = "^1.0"
serde_derive = "^1.0"
serde_json = "^1.0"
wasm-bindgen-futures = "0.4.33"

[dependencies.web-sys]
version = "0.3"
features = ["HtmlAnchorElement", "console", "Window", "Document"]

[dependencies.reqwest]
version = "^0.11"
features = ["json", "blocking"]
