mod response_time_map;


use crate::components::control::{Cities, Control};
use crate::components::map_component::{City, MapComponent, Point};
use crate::response_time_map::time_map_request;
use yew::prelude::*;

mod components;

enum Msg {
    SelectCity(City)
}

struct Model {
    city: City,
    cities: Cities
}

impl Component for Model {
    type Message = Msg;
    type Properties = ();

    fn create(_ctx: &Context<Self>) -> Self {
        let aachen = City {
            name: "Trafalgar Square".to_string(),
            lat: Point(51.507609, -0.128315),
        };
        let stuttgart = City {
            name: "Reading".to_string(),
            lat: Point(51.458296231530774, -0.9718083064213707),
        };
        let cities: Cities = Cities {
            list: vec![aachen, stuttgart],
        };
        let city = cities.list[0].clone();
        Self { city, cities}
    }

    fn update(&mut self, _ctx: &Context<Self>, msg: Self::Message) -> bool {
        match msg {
            Msg::SelectCity(city) => {
                self.city = self
                    .cities
                    .list
                    .iter()
                    .find(|c| c.name == city.name)
                    .unwrap()
                    .clone();
            }
        }
        true
    }

    fn changed(&mut self, _ctx: &Context<Self>) -> bool {
        false
    }

    fn view(&self, ctx: &Context<Self>) -> Html {
        let cb = ctx.link().callback(|name| Msg::SelectCity(name));
        html! {
            <>
                <MapComponent city={&self.city} />
                <Control select_city={cb} cities={&self.cities}/>
            </>
        }
    }
}

fn main() {
    yew::start_app::<Model>();
}


// #[tokio::main]
// async fn main() {
//     // let result = example_time_map().await;
//     let time_map = time_map_request(Point(51.507609, -0.128315)).await;
//     println!("{:?}", time_map);
// }
