use gloo_utils::format::JsValueSerdeExt;
use leaflet::{LayerGroup, Polygon};
use leaflet::{LatLng, Map, TileLayer};
use traveltime::models::{ResponseTimeMap, Coords, ResponseTimeMapResult};
use wasm_bindgen::prelude::*;
use wasm_bindgen::JsCast;
use yew::html::ImplicitClone;
use yew::prelude::*;
use gloo_utils::document;
use web_sys::{
    Element,
    HtmlElement,
    Node,
};

use serde::{Deserialize, Serialize};

use crate::response_time_map::time_map_request;


#[derive(Clone, PartialEq, Debug)]
pub struct TimeMap{
    time_map: ResponseTimeMap
}
impl ImplicitClone for TimeMap {}

pub enum Msg {
    TimeMap(TimeMap)
}

pub struct MapComponent {
    map: Map,
    time_layer: LayerGroup,
    lat: Point,
    container: HtmlElement,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq)]
pub struct Point(pub f64, pub f64);

#[derive(PartialEq, Clone, Debug)]
pub struct City {
    pub name: String,
    pub lat: Point,
}

impl ImplicitClone for City {}

#[derive(Serialize, Deserialize)]
#[allow(non_snake_case)]
struct LineOptions {
    color: String,
    weight: u32,
    fillOpacity: f64,
}

#[derive(PartialEq, Properties, Clone)]
pub struct Props {
    pub city: City
}

impl MapComponent {
    fn render_map(&self) -> Html {
        let node: &Node = &self.container.clone().into();
        Html::VRef(node.clone())
    }
}

impl Component for MapComponent {
    type Message = Msg;
    type Properties = Props;

    fn create(ctx: &Context<Self>) -> Self {
        let props = ctx.props();

        let container: Element = document().create_element("div").unwrap();
        let container: HtmlElement = container.dyn_into().unwrap();
        container.set_class_name("map");
        let leaflet_map = Map::new_with_element(&container, &JsValue::NULL);
        let time_map = LayerGroup::new();
        Self {
            map: leaflet_map,
            time_layer: time_map,
            container,
            lat: props.city.lat,
        }
    }

    fn rendered(&mut self, _ctx: &Context<Self>, first_render: bool) {
        if first_render {
            self.map.setView(&LatLng::new(self.lat.0, self.lat.1), 11.0);
            add_tile_layer(&self.map);
            self.time_layer.addTo(&self.map);
        }
    }

    fn update(&mut self, ctx: &Context<Self>, msg: Self::Message) -> bool {
        match msg {
            Msg::TimeMap(time_map) => {
                self.time_layer.clearLayers();
                for result in extract_multi_polygons(&time_map.time_map) {
                    for shape in result {
                        self.time_layer.addLayer(
                            &Polygon::new_with_options(
                                shape
                                .into_iter()
                                .map(LatLng::from)
                                .map(JsValue::from)
                                .collect(),
                            &JsValue::from_serde(&LineOptions {
                                color: "green".into(),
                                weight: 3,
                                fillOpacity: 0.0,
                            })
                            .expect("Unable to serialize polyline options"),
                        ));
                    }
                }
            }
            _ => ()
        }
        false
    }

    fn changed(&mut self, ctx: &Context<Self>) -> bool {
        let props = ctx.props();
        if self.lat == props.city.lat {
            return false
        }
        self.lat = props.city.lat;
        self.map.setView(&LatLng::new(self.lat.0, self.lat.1), 11.0);
        let point = props.city.lat.clone();
        ctx.link().send_future(async move {
            Msg::TimeMap(TimeMap{
                time_map: time_map_request(point).await.expect("No time_map response")
            })
        });
        true
    }

    fn view(&self, _ctx: &Context<Self>) -> Html {
        html! {
            <div class="map-container component-container">
                {self.render_map()}
            </div>
        }
    }
}

fn extract_multi_polygons(time_map: &ResponseTimeMap) ->Vec<Vec<Vec<Coords>>> {
    time_map.results.iter().map(
        extract_shape
    ).collect()
}

fn extract_shape(time_map_result: &ResponseTimeMapResult) -> Vec<Vec<Coords>> {
    time_map_result
        .shapes.iter().map(|shape| shape.shell.clone()).collect()
}

fn add_tile_layer(map: &Map) {
    TileLayer::new(
        "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
        &JsValue::NULL,
    )
    .addTo(map);
}
