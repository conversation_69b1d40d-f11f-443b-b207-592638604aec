"""
Find all postcodes within a given time of a given location via a
given transport method using the TravelTime API
"""

# %%
from pinpoint.api.traveltime import postcode_search
from pinpoint.api.traveltime_model import ArrivalSearch, Coordinates, MaxChanges, SearchRange, TimeMapRequest, Transportation, TravelTimeMatrixResponse


example_request = TimeMapRequest(
    arrival_searches=[
        ArrivalSearch(
            id="public transport to Trafalgar Square",
            coords=Coordinates(
                lat=51.507609,
                lng=-0.128315
            ),
            transportation=Transportation(
                type="public_transport",
                max_changes=MaxChanges(
                    enabled=True,
                    limit=7
                )
            ),
            arrival_time="2022-07-25T13:00:00Z",
            travel_time=900,
            range_=SearchRange(
                enabled=True,
                width=3600
            ),
            properties=["travel_time", "distance"]
        )
    ]
)

response_raw = postcode_search(example_request)
# response_raw = time_map(example_request)
# response = TimeMapResponse(**response_raw)
response = TravelTimeMatrixResponse(**response_raw)

# %%
