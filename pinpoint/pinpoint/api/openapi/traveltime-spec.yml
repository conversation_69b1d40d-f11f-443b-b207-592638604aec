openapi: "3.0.3"

info:
  title: TravelTime API
  termsOfService: https://www.traveltime.com/terms-and-conditions
  contact:
    name: API Support
    email: <EMAIL>
  version: 1.2.3

servers:
  - url: https://1k8cheq5kc.execute-api.eu-west-2.amazonaws.com

externalDocs:
  url: http://docs.traveltime.com

security:
  - ApplicationId: []
    ApiKey: []

components:
  schemas:
    Coords:
      type: object
      required:
        - lat
        - lng
      properties:
        lat:
          type: number
          format: double
        lng:
          type: number
          format: double

    RequestSearchId:
      type: string

    RequestDepartureArrivalLocationOne:
      type: string

    RequestTravelTime:
      type: integer
      minimum: 60
      maximum: 14400

    RequestDepartureArrivalTime:
      type: string
      format: date-time

    ResponseLocalTime:
      type: string

    RequestRoutesProperty:
      type: string
      enum:
        - travel_time
        - distance
        - fares
        - route

    RequestTimeFilterProperty:
      type: string
      enum:
        - travel_time
        - distance
        - distance_breakdown
        - fares
        - route

    RequestRangeEnabled:
      type: boolean

    RequestRangeWidth:
      type: integer
      minimum: 1
      maximum: 43200

    RequestRangeFull:
      type: object
      required:
        - enabled
        - max_results
        - width
      properties:
        enabled:
          $ref: '#/components/schemas/RequestRangeEnabled'
        max_results:
          type: integer
          minimum: 1
          maximum: 5
        width:
          $ref: '#/components/schemas/RequestRangeWidth'

    RequestRangeNoMaxResults:
      type: object
      required:
        - enabled
        - width
      properties:
        enabled:
          $ref: '#/components/schemas/RequestRangeEnabled'
        width:
          $ref: '#/components/schemas/RequestRangeWidth'

    RequestTimeFilterPostcodesProperty:
      type: string
      enum:
        - travel_time
        - distance
    
    RequestTimeFilterPostcodeDistrictsReachablePostcodesThreshold:
      type: number
      format: double

    RequestTimeFilterPostcodeDistrictsProperty:
      type: string
      enum:
        - travel_time_reachable
        - travel_time_all
        - coverage
    
    RequestTimeFilterPostcodeSectorsReachablePostcodesThreshold:
      type: number
      format: double

    RequestTimeFilterPostcodeSectorsProperty:
      type: string
      enum:
        - travel_time_reachable
        - travel_time_all
        - coverage
    
    RequestTimeFilterPostcodeSectorsDepartureSearch:
      type: object
      required:
        - id
        - coords
        - transportation
        - travel_time
        - departure_time
        - reachable_postcodes_threshold
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        departure_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        reachable_postcodes_threshold:
          $ref: '#/components/schemas/RequestTimeFilterPostcodeSectorsReachablePostcodesThreshold'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeSectorsProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'
    
    RequestTimeFilterPostcodeSectorsArrivalSearch:
      type: object
      required:
        - id
        - coords
        - transportation
        - travel_time
        - arrival_time
        - reachable_postcodes_threshold
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        arrival_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        reachable_postcodes_threshold:
          $ref: '#/components/schemas/RequestTimeFilterPostcodeSectorsReachablePostcodesThreshold'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeSectorsProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'
    
    RequestTimeFilterPostcodeDistrictsDepartureSearch:
      type: object
      required:
        - id
        - coords        
        - transportation
        - travel_time
        - departure_time
        - reachable_postcodes_threshold
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        departure_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        reachable_postcodes_threshold:
          $ref: '#/components/schemas/RequestTimeFilterPostcodeDistrictsReachablePostcodesThreshold'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeDistrictsProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'
    
    RequestTimeFilterPostcodeDistrictsArrivalSearch:
      type: object
      required:
        - id
        - coords        
        - transportation
        - travel_time
        - arrival_time
        - reachable_postcodes_threshold
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        arrival_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        reachable_postcodes_threshold:
          $ref: '#/components/schemas/RequestTimeFilterPostcodeDistrictsReachablePostcodesThreshold'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeDistrictsProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'
    
    RequestTimeFilterPostcodesDepartureSearch:
      type: object
      required:
        - id
        - coords        
        - transportation
        - travel_time
        - departure_time
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        departure_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodesProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'
    
    RequestTimeFilterPostcodesArrivalSearch:
      type: object
      required:
        - id
        - coords        
        - transportation
        - travel_time
        - arrival_time
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        arrival_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodesProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'
    
    RequestTimeFilterDepartureSearch:
      type: object
      required:
        - id
        - departure_location_id
        - arrival_location_ids
        - transportation
        - travel_time
        - departure_time
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        departure_location_id:
          type: string
        arrival_location_ids:
          type: array
          minItems: 1
          maxItems: 2000
          items:
            type: string
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        departure_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'

    RequestTimeFilterArrivalSearch:
      type: object
      required:
        - id
        - departure_location_ids
        - arrival_location_id
        - transportation
        - travel_time
        - arrival_time
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        departure_location_ids:
          type: array
          minItems: 1
          maxItems: 2000
          items:
            type: string
        arrival_location_id:
          type: string
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        arrival_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'

    RequestArrivalTimePeriod:
      type: string
      enum:
        - weekday_morning
    
    RequestTimeFilterFastProperty:
      type: string
      enum:
        - travel_time
        - fares
    
    RequestTimeFilterFastArrivalManyToOneSearch:
      type: object
      required:
        - id
        - arrival_location_id
        - departure_location_ids
        - transportation
        - travel_time
        - arrival_time_period
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        arrival_location_id:
          type: string
        departure_location_ids:
          type: array
          minItems: 1
          maxItems: 100000
          items:
            type: string
        transportation:
          $ref: '#/components/schemas/RequestTransportationFast'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        arrival_time_period:
          $ref: '#/components/schemas/RequestArrivalTimePeriod'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterFastProperty'

    RequestTimeFilterFastArrivalOneToManySearch:
      type: object
      required:
        - id
        - departure_location_id
        - arrival_location_ids
        - transportation
        - travel_time
        - arrival_time_period
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        departure_location_id:
          type: string
        arrival_location_ids:
          type: array
          minItems: 1
          maxItems: 100000
          items:
            type: string
        transportation:
          $ref: '#/components/schemas/RequestTransportationFast'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        arrival_time_period:
          $ref: '#/components/schemas/RequestArrivalTimePeriod'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeFilterFastProperty'

    RequestTransportationFast:
      type: object
      required:
        - type
      properties:
        type:
          type: string
          enum:
            - public_transport
            - driving
            - driving+public_transport
    
    RequestRoutesDepartureSearch:
      type: object
      required:
        - id
        - departure_location_id
        - arrival_location_ids
        - transportation
        - departure_time
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        departure_location_id:
          type: string
        arrival_location_ids:
          type: array
          minItems: 1
          maxItems: 2
          items:
            type: string
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        departure_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestRoutesProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'

    RequestRoutesArrivalSearch:
      type: object
      required:
        - id
        - departure_location_ids
        - arrival_location_id
        - transportation
        - arrival_time
        - properties
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        departure_location_ids:
          type: array
          minItems: 1
          maxItems: 2
          items:
            type: string
        arrival_location_id:
          type: string
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        arrival_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestRoutesProperty'
        range:
          $ref: '#/components/schemas/RequestRangeFull'

    RequestTimeFilter:
      type: object
      required:
        - locations
      properties:
        locations:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/RequestLocation'
        departure_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterDepartureSearch'
        arrival_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterArrivalSearch'
    
    RequestTimeFilterPostcodes:
      type: object
      properties:
        departure_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodesDepartureSearch'
        arrival_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodesArrivalSearch'

    RequestTimeFilterPostcodeDistricts:
      type: object
      properties:
        departure_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeDistrictsDepartureSearch'
        arrival_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeDistrictsArrivalSearch'

    RequestTimeFilterPostcodeSectors:
      type: object
      properties:
        departure_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeSectorsDepartureSearch'
        arrival_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeSectorsArrivalSearch'

    RequestTimeFilterFastArrivalSearches:
      type: object
      properties:
        many_to_one:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterFastArrivalManyToOneSearch'
        one_to_many:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeFilterFastArrivalOneToManySearch'

    RequestTimeFilterFast:
      type: object
      required:
        - locations
        - arrival_searches
      properties:
        locations:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/RequestLocation'
        arrival_searches:
            $ref: '#/components/schemas/RequestTimeFilterFastArrivalSearches'

    RequestRoutes:
      type: object
      required:
        - locations
      properties:
        locations:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/RequestLocation'
        departure_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestRoutesDepartureSearch'
        arrival_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestRoutesArrivalSearch'

    RequestTimeMapProperty:
      type: string
      enum:
        - is_only_walking

    RequestLevelOfDetail:
      type: object
      required:
        - scale_type
        - level
      properties:
        scale_type:
          type: string
          enum:
            - simple
        level:
          type: string
          enum:
            - lowest
            - low
            - medium
            - high
            - highest

    RequestUnionOnIntersection:
      type: object
      required:
        - id
        - search_ids
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        search_ids:
          type: array
          items:
            type: string

    RequestTimeMapDepartureSearch:
      type: object
      required:
        - id
        - coords
        - transportation
        - travel_time
        - departure_time
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        coords:
          $ref: '#/components/schemas/Coords'
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        departure_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeMapProperty'
        range:
          $ref: '#/components/schemas/RequestRangeNoMaxResults'
        level_of_detail:
          $ref: '#/components/schemas/RequestLevelOfDetail'

    RequestTimeMapArrivalSearch:
      type: object
      required:
        - id
        - coords
        - transportation
        - travel_time
        - arrival_time
      properties:
        id:
          $ref: '#/components/schemas/RequestSearchId'
        coords:
          $ref: '#/components/schemas/Coords'
        transportation:
          $ref: '#/components/schemas/RequestTransportation'
        travel_time:
          $ref: '#/components/schemas/RequestTravelTime'
        arrival_time:
          $ref: '#/components/schemas/RequestDepartureArrivalTime'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/RequestTimeMapProperty'
        range:
          $ref: '#/components/schemas/RequestRangeNoMaxResults'
        level_of_detail:
          $ref: '#/components/schemas/RequestLevelOfDetail'

    RequestTimeMap:
      type: object
      properties:
        departure_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeMapDepartureSearch'
        arrival_searches:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestTimeMapArrivalSearch'
        unions:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestUnionOnIntersection'
        intersections:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/RequestUnionOnIntersection'

    ResponseSearchId:
      type: string

    ResponseLocationId:
      type: string

    ResponseTravelTime:
      type: integer

    ResponseDistance:
      type: integer

    ResponseTransportationMode:
      type: string
      enum:
        - car
        - parking
        - boarding
        - walk
        - bike
        - train
        - rail_national
        - rail_overground
        - rail_underground
        - rail_dlr
        - bus
        - cable_car
        - plane
        - ferry
        - coach

    ResponseDistanceBreakdownItem:
      type: object
      required:
       - mode
       - distance
      properties:
        mode:
          $ref: '#/components/schemas/ResponseTransportationMode'
        distance:
          $ref: '#/components/schemas/ResponseDistance'

    ResponseFareTicket:
      type: object
      required:
        - type
        - price
        - currency
      properties:
        type:
          type: string
          enum:
            - single
            - week
            - month
            - year
        price:
          type: number
          format: double
        currency:
          type: string

    ResponseFaresBreakdownItem:
      type: object
      required:
        - modes
        - route_part_ids
        - tickets
      properties:
        modes:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTransportationMode'
        route_part_ids:
          type: array
          items:
            type: integer
        tickets:
          type: array
          items:
            $ref: '#/components/schemas/ResponseFareTicket'

    ResponseFares:
      type: object
      required:
        - breakdown
        - tickets_total
      properties:
        breakdown:
          type: array
          items:
            $ref: '#/components/schemas/ResponseFaresBreakdownItem'
        tickets_total:
          type: array
          items:
            $ref: '#/components/schemas/ResponseFareTicket'
    
    ResponseFaresFast:
      type: object
      required:
        - tickets_total
      properties:
        tickets_total:
          type: array
          items:
            $ref: '#/components/schemas/ResponseFareTicket'

    ResponseRoutePart:
      type: object
      required:
        - id
        - type
        - mode
        - directions
        - distance
        - travel_time
        - coords
      properties:
        id:
          type: string
        type:
          type: string
          enum:
            - basic
            - start_end
            - road
            - public_transport
        mode:
          $ref: '#/components/schemas/ResponseTransportationMode'
        directions:
          type: string
        distance:
          $ref: '#/components/schemas/ResponseDistance'
        travel_time:
          $ref: '#/components/schemas/ResponseTravelTime'
        coords:
          type: array
          items:
            $ref: '#/components/schemas/Coords'
        direction:
          type: string
        road:
          type: string
        turn:
          type: string
        line:
          type: string
        departure_station:
          type: string
        arrival_station:
          type: string
        departs_at:
          $ref: '#/components/schemas/ResponseLocalTime'
        arrives_at:
          $ref: '#/components/schemas/ResponseLocalTime'
        num_stops:
          type: integer

    ResponseRoute:
      type: object
      required:
        - departure_time
        - arrival_time
        - parts
      properties:
        departure_time:
          type: string
          format: date-time
        arrival_time:
          type: string
          format: date-time
        parts:
          type: array
          items:
            $ref: '#/components/schemas/ResponseRoutePart'

    ResponseTimeFilterProperties:
      type: object
      properties:
        travel_time:
          $ref: '#/components/schemas/ResponseTravelTime'
        distance:
          $ref: '#/components/schemas/ResponseDistance'
        distance_breakdown:
          type: array
          items:
            $ref: '#/components/schemas/ResponseDistanceBreakdownItem'
        fares:
          $ref: '#/components/schemas/ResponseFares'
        route:
          $ref: '#/components/schemas/ResponseRoute'

    ResponseTravelTimeStatistics:
      type: object
      required:
        - min
        - max
        - mean
        - median
      properties:
        min:
          type: integer
        max:
          type: integer
        mean:
          type: integer
        median:
          type: integer 

    ResponseTimeFilterPostcodesProperties:
      type: object
      properties:
        travel_time:
          $ref: '#/components/schemas/ResponseTravelTime'
        distance:
          $ref: '#/components/schemas/ResponseDistance'

    ResponseTimeFilterPostcodeDistrictProperties:
      type: object
      properties:
        travel_time_reachable:
          $ref: '#/components/schemas/ResponseTravelTimeStatistics'
        travel_time_all:
          $ref: '#/components/schemas/ResponseTravelTimeStatistics'
        coverage:
          type: number
          format: double
    
    ResponseTimeFilterPostcodeSectorProperties:
      type: object
      properties:
        travel_time_reachable:
          $ref: '#/components/schemas/ResponseTravelTimeStatistics'
        travel_time_all:
          $ref: '#/components/schemas/ResponseTravelTimeStatistics'
        coverage:
          type: number
          format: double

    ResponseTimeFilterFastProperties:
      type: object
      properties:
        travel_time:
          $ref: '#/components/schemas/ResponseTravelTime'
        fares:
          $ref: '#/components/schemas/ResponseFaresFast'

    ResponseRoutesProperties:
      type: object
      properties:
        travel_time:
          $ref: '#/components/schemas/ResponseTravelTime'
        distance:
          $ref: '#/components/schemas/ResponseDistance'
        fares:
          $ref: '#/components/schemas/ResponseFares'
        route:
          $ref: '#/components/schemas/ResponseRoute'

    ResponseTimeFilterLocation:
      type: object
      required:
        - id
        - properties
      properties:
        id:
          $ref: '#/components/schemas/ResponseLocationId'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterProperties'
    
    ResponseTimeFilterFastLocation:
      type: object
      required:
        - id
        - properties
      properties:
        id:
          $ref: '#/components/schemas/ResponseLocationId'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterFastProperties'

    ResponseRoutesLocation:
      type: object
      required:
        - id
        - properties
      properties:
        id:
          $ref: '#/components/schemas/ResponseLocationId'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/ResponseRoutesProperties'
    
    ResponseTimeFilterPostcode:
      type: object
      required:
        - code
        - properties
      properties:
        code:
          type: string
        properties:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodesProperties'

    ResponseTimeFilterPostcodeDistrict:
      type: object
      required:
        - code
        - properties
      properties:
        code:
          type: string
        properties:
          $ref: '#/components/schemas/ResponseTimeFilterPostcodeDistrictProperties'
    
    ResponseTimeFilterPostcodeSector:
      type: object
      required:
        - code
        - properties
      properties:
        code:
          type: string
        properties:
          $ref: '#/components/schemas/ResponseTimeFilterPostcodeSectorProperties'

    ResponseTimeFilterResult:
      type: object
      required:
        - search_id
        - locations
        - unreachable
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        locations:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterLocation'
        unreachable:
          type: array
          items:
            type: string
    
    ResponseTimeFilterPostcodesResult:
      type: object
      required:
        - search_id
        - postcodes
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        postcodes:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterPostcode'

    ResponseTimeFilterPostcodeDistrictsResult:
      type: object
      required:
        - search_id
        - districts
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        districts:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodeDistrict'
    
    ResponseTimeFilterPostcodeSectorsResult:
      type: object
      required:
        - search_id
        - sectors
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        sectors:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodeSector'

    ResponseTimeFilterFastResult:
      type: object
      required:
        - search_id
        - locations
        - unreachable
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        locations:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterFastLocation'
        unreachable:
          type: array
          items:
            type: string

    ResponseRoutesResult:
      type: object
      required:
        - search_id
        - locations
        - unreachable
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        locations:
          type: array
          items:
            $ref: '#/components/schemas/ResponseRoutesLocation'
        unreachable:
          type: array
          items:
            type: string

    ResponseTimeFilter:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterResult'
    
    ResponseTimeFilterPostcodes:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodesResult'

    ResponseTimeFilterPostcodeDistricts:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodeDistrictsResult'
    
    ResponseTimeFilterPostcodeSectors:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodeSectorsResult'

    ResponseTimeFilterFast:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeFilterFastResult'

    ResponseRoutes:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseRoutesResult'

    ResponseShape:
      type: object
      required:
        - shell
        - holes
      properties:
        shell:
          type: array
          items:
            $ref: '#/components/schemas/Coords'
        holes:
          type: array
          items:
            type: array
            items:
              $ref: '#/components/schemas/Coords'

    ResponseBox:
      type: object
      required:
        - min_lat
        - max_lat
        - min_lng
        - max_lng
      properties:
        min_lat:
          type: number
          format: double
        max_lat:
          type: number
          format: double
        min_lng:
          type: number
          format: double
        max_lng:
          type: number
          format: double

    ResponseBoundingBox:
      type: object
      required:
        - envelope
        - boxes
      properties:
        envelope:
          $ref: '#/components/schemas/ResponseBox'
        boxes:
          type: array
          items:
            $ref: '#/components/schemas/ResponseBox'

    ResponseTimeMapProperties:
      type: object
      properties:
        is_only_walking:
          type: boolean

    ResponseWktShape:
      type: string

    ResponseTimeMapResult:
      type: object
      required:
        - search_id
        - shapes
        - properties
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        shapes:
          type: array
          items:
            $ref: '#/components/schemas/ResponseShape'
        properties:
          $ref: '#/components/schemas/ResponseTimeMapProperties'

    ResponseTimeMap:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeMapResult'

    ResponseTimeMapWktResult:
      type: object
      required:
        - search_id
        - shape
        - properties
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        shape:
          $ref: '#/components/schemas/ResponseWktShape'
        properties:
          $ref: '#/components/schemas/ResponseTimeMapProperties'

    ResponseTimeMapWkt:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeMapWktResult'

    ResponseTimeMapBoundingBoxesResult:
      type: object
      required:
        - search_id
        - bounding_boxes
        - properties
      properties:
        search_id:
          $ref: '#/components/schemas/ResponseSearchId'
        bounding_boxes:
          type: array
          items:
            $ref: '#/components/schemas/ResponseBoundingBox'
        properties:
          $ref: '#/components/schemas/ResponseTimeMapProperties'

    ResponseTimeMapBoundingBoxes:
      type: object
      required:
        - results
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ResponseTimeMapBoundingBoxesResult'

    TransportationMaxChanges:
      type: object
      properties:
        enabled:
          type: boolean
        limit:
          type: integer

    RequestTransportation:
      type: object
      required:
        - type
      properties:
        type:
          type: string
          enum:
            - cycling
            - driving
            - driving+train
            - public_transport
            - walking
            - coach
            - bus
            - train
            - ferry
            - driving+ferry
            - cycling+ferry
            - cycling+public_transport
        disable_border_crossing:
          type: boolean
        pt_change_delay:
          type: integer
        walking_time:
          type: integer
        driving_time_to_station:
          type: integer
        cycling_time_to_station:
          type: integer
        parking_time:
          type: integer
        boarding_time:
          type: integer
        max_changes:
          $ref: '#/components/schemas/TransportationMaxChanges'

    RequestLocationId:
      type: string

    ResponseSupportedLocation:
      type: object
      required:
        - id
        - map_name
      properties:
        id:
          type: string
        map_name:
          type: string

    ResponseSupportedLocations:
      type: object
      required:
        - locations
        - unsupported_locations
      properties:
        locations:
          type: array
          items:
            $ref: '#/components/schemas/ResponseSupportedLocation'
        unsupported_locations:
          type: array
          items:
            type: string

    RequestSupportedLocations:
      type: object
      required:
        - locations
      properties:
        locations:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/RequestLocation'

    RequestLocation:
      type: object
      required:
        - id
        - coords
      properties:
        id:
          $ref: '#/components/schemas/RequestLocationId'
        coords:
          $ref: '#/components/schemas/Coords'

    ResponseError:
      externalDocs:
        url: http://docs.traveltime.com/reference/error-response
      type: object
      properties:
        http_status:
          type: integer
        error_code:
          type: integer
        description:
          type: string
        documentation_link:
          type: string
        additional_info:
          type: object
          additionalProperties:
            type: array
            items:
              type: string

    ResponseGeocodingGeometry:
      type: object
      required:
        - type
        - coordinates
      properties:
        type:
          type: string
        coordinates:
          type: array
          items:
            type: number
            format: double

    ResponseMapInfoFeaturesPublicTransport:
      type: object
      required:
        - date_start
        - date_end
      properties:
        date_start:
          type: string
          format: date-time
        date_end:
          type: string
          format: date-time
    
    ResponseMapInfoFeatures:
      type: object
      required:
        - fares
        - postcodes
      properties:
        public_transport:
          $ref: '#/components/schemas/ResponseMapInfoFeaturesPublicTransport'
        fares:
          type: boolean
        postcodes:
          type: boolean

    ResponseGeocodingProperties:
      type: object
      required:
        - name
        - label
      properties:
        name:
          type: string
        label:
          type: string
        score:
          type: number
          format: double
        house_number:
          type: string
        street:
          type: string
        region:
          type: string
        region_code:
          type: string
        neighbourhood:
          type: string
        county:
          type: string
        macroregion:
          type: string
        city:
          type: string
        country:
          type: string
        country_code:
          type: string
        continent:
          type: string
        postcode:
          type: string
        features:
          $ref: '#/components/schemas/ResponseMapInfoFeatures'
    
    ResponseGeocodingGeoJsonFeature:
      type: object
      required:
        - type
        - geometry
        - properties
      properties:
        type:
          type: string
        geometry:
          $ref: '#/components/schemas/ResponseGeocodingGeometry'
        properties:
          $ref: '#/components/schemas/ResponseGeocodingProperties'
    
    ResponseGeocoding:
      type: object
      required:
        - type
        - features
      properties:
        type:
          type: string
        features:
          type: array
          items:
            $ref: '#/components/schemas/ResponseGeocodingGeoJsonFeature'

    ResponseMapInfoMap:
      type: object
      required:
        - name
        - features
      properties:
        name:
          type: string
        features:
          $ref: '#/components/schemas/ResponseMapInfoFeatures'

    ResponseMapInfo:
      type: object
      required:
        - maps
      properties:
        maps:
          type: array
          items:
            $ref: '#/components/schemas/ResponseMapInfoMap'

  requestBodies:
    TimeFilter:
      required: true
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RequestTimeFilter'
    
    TimeFilterPostcodes:
      required: true
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RequestTimeFilterPostcodes'

    TimeFilterPostcodeDistricts:
      required: true
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeDistricts'

    TimeFilterPostcodeSectors:
      required: true
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RequestTimeFilterPostcodeSectors'

    TimeFilterFast:
      required: true
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RequestTimeFilterFast'

    Routes:
      required: true
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RequestRoutes'

    TimeMap:
      required: true
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RequestTimeMap'

    SupportedLocations:
      required: true
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RequestSupportedLocations'

  responses:
    Error:
      description: 'The json body returned upon error. [Docs link](http://docs.traveltime.com/reference/error-response)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseError'

    TimeFilter:
      description: 'Given origin and destination points filter out points that cannot be reached within specified time limit. [Docs link](http://docs.traveltime.com/reference/time-filter)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseTimeFilter'
    
    TimeFilterPostcodes:
      description: 'Find reachable postcodes from origin and get statistics about such postcodes. [Docs link](http://docs.traveltime.com/reference/postcode-search/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodes'

    TimeFilterPostcodeDistricts:
      description: 'Find districts that have a certain coverage from origin and get statistics about postcodes within such districts. [Docs link](http://docs.traveltime.com/reference/postcode-district-filter/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodeDistricts'
    
    TimeFilterPostcodeSectors:
      description: 'Find sectors that have a certain coverage from origin and get statistics about postcodes within such sectors. [Docs link](http://docs.traveltime.com/reference/postcode-sector-filter/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseTimeFilterPostcodeSectors'
    
    TimeFilterFast:
      description: 'A very fast version of Time Filter. [Docs link](http://docs.traveltime.com/reference/time-filter-fast/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseTimeFilterFast'

    Routes:
      description: 'Returns routing information between source and destinations. [Docs link](http://docs.traveltime.com/reference/routes/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseRoutes'
    
    Geocoding:
      description: 'Match a query string to geographic coordinates. [Docs link](http://docs.traveltime.com/reference/geocoding-search/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseGeocoding'

    TimeMap:
      description: 'Given origin coordinates, find shapes of zones reachable within corresponding travel time. [Docs link](http://docs.traveltime.com/reference/time-map/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseTimeMap'
        'application/vnd.wkt+json':
          schema:
            $ref: '#/components/schemas/ResponseTimeMapWkt'
        'application/vnd.wkt-no-holes+json':
          schema:
            $ref: '#/components/schemas/ResponseTimeMapWkt'
        'application/vnd.bounding-boxes+json':
          schema:
            $ref: '#/components/schemas/ResponseTimeMapBoundingBoxes'

    MapInfo:
      description: 'Returns information about currently supported countries. [Docs link](http://docs.traveltime.com/reference/map-info/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseMapInfo'

    SupportedLocations:
      description: 'Find out what points are supported by our api. [Docs link](http://docs.traveltime.com/reference/supported-locations/)'
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/ResponseSupportedLocations'

  parameters:
    GeocodingWithinCountry:
      name: within.country
      in: query
      schema:
        type: string
    
    GeocodingQuery:
      name: query
      in: query
      required: true
      schema:
        type: string
    
    GeocodingFocusLat:
      name: focus.lat
      in: query
      schema:
        type: number
        format: double
    
    GeocodingFocusLng:
      name: focus.lng
      in: query
      schema:
        type: number
        format: double
    
    GeocodingReverseLat:
      name: lat
      in: query
      required: true
      schema:
        type: number
        format: double
    
    GeocodingReverseLng:
      name: lng
      in: query
      required: true
      schema:
        type: number
        format: double


paths:
  /time-filter:
    post:
      externalDocs:
        url: http://docs.traveltime.com/reference/time-filter
      operationId: timeFilter
      requestBody:
        $ref: '#/components/requestBodies/TimeFilter'
      responses:
        '200':
          $ref: '#/components/responses/TimeFilter'
        default:
          $ref: '#/components/responses/Error'
      
  /time-filter/postcodes:
    post:
      externalDocs:
        url: http://docs.traveltime.com/reference/postcode-search
      operationId: timeFilterPostcodes
      requestBody:
        $ref: '#/components/requestBodies/TimeFilterPostcodes'
      responses:
        '200':
          $ref: '#/components/responses/TimeFilterPostcodes'
        default:
          $ref: '#/components/responses/Error'
  
  /time-filter/postcode-districts:
    post:
      externalDocs:
        url: http://docs.traveltime.com/reference/postcode-search
      operationId: timeFilterPostcodeDistricts
      requestBody:
        $ref: '#/components/requestBodies/TimeFilterPostcodeDistricts'
      responses:
        '200':
          $ref: '#/components/responses/TimeFilterPostcodeDistricts'
        default:
          $ref: '#/components/responses/Error'
  
  /time-filter/postcode-sectors:
    post:
      externalDocs:
        url: http://docs.traveltime.com/reference/postcode-sector-filter
      operationId: timeFilterPostcodeSectors
      requestBody:
        $ref: '#/components/requestBodies/TimeFilterPostcodeSectors'
      responses:
        '200':
          $ref: '#/components/responses/TimeFilterPostcodeSectors'
        default:
          $ref: '#/components/responses/Error'
  
  /time-filter/fast:
    post:
      externalDocs:
        url: http://docs.traveltime.com/reference/time-filter-fast
      operationId: timeFilterFast
      requestBody:
        $ref: '#/components/requestBodies/TimeFilterFast'
      responses:
        '200':
          $ref: '#/components/responses/TimeFilterFast'
        default:
          $ref: '#/components/responses/Error'

  /time-map:
    post:
      externalDocs:
        url: http://docs.traveltime.com/reference/time-map
      operationId: timeMap
      requestBody:
        $ref: '#/components/requestBodies/TimeMap'
      responses:
        '200':
          $ref: '#/components/responses/TimeMap'
        default:
          $ref: '#/components/responses/Error'

  /routes:
    post:
      externalDocs:
        url: http://docs.traveltime.com/reference/routes
      operationId: routes
      requestBody:
        $ref: '#/components/requestBodies/Routes'
      responses:
        '200':
          $ref: '#/components/responses/Routes'
        default:
          $ref: '#/components/responses/Error'
  
  /geocoding/search:
    get:
      externalDocs:
        url: http://docs.traveltime.com/reference/geocoding-search
      operationId: geocodingSearch
      responses:
        '200':
          $ref: '#/components/responses/Geocoding'
        default:
          $ref: '#/components/responses/Error'
      parameters:
        - $ref: '#/components/parameters/GeocodingQuery'
        - $ref: '#/components/parameters/GeocodingFocusLat'
        - $ref: '#/components/parameters/GeocodingFocusLng'
        - $ref: '#/components/parameters/GeocodingWithinCountry'
  
  /geocoding/reverse:
    get:
      externalDocs:
        url: http://docs.traveltime.com/reference/geocoding-reverse
      operationId: geocodingReverseSearch
      responses:
        '200':
          $ref: '#/components/responses/Geocoding'
        default:
          $ref: '#/components/responses/Error'
      parameters:
        - $ref: '#/components/parameters/GeocodingReverseLat'
        - $ref: '#/components/parameters/GeocodingReverseLng'
        - $ref: '#/components/parameters/GeocodingWithinCountry'
  
  /map-info:
    get:
      externalDocs:
        url: http://docs.traveltime.com/reference/map-info
      operationId: mapInfo
      responses:
        '200':
          $ref: '#/components/responses/MapInfo'
        default:
          $ref: '#/components/responses/Error'

  /supported-locations:
    post:
      externalDocs:
        url: http://docs.traveltime.com/reference/supported-locations
      operationId: supportedLocations
      requestBody:
        $ref: '#/components/requestBodies/SupportedLocations'
      responses:
        '200':
          $ref: '#/components/responses/SupportedLocations'
        default:
          $ref: '#/components/responses/Error'