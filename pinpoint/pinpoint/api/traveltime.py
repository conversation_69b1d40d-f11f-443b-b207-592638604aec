# %%
import json
import os
from datetime import timedelta
from pathlib import Path
from typing import (
    Optional,
    Sequence
)

import requests
from devtools import debug
from dotenv import dotenv_values
from pinpoint.api.traveltime_model import (
    ArrivalSearch,
    Coordinates,
    MaxChanges,
    SearchRange,
    TimeMapRequest,
    TimeMapResponse,
    Transportation,
    TravelTimeMatrixResponse
)
from requests.auth import HTTPBasicAuth
from requests_cache import CachedSession

env = dotenv_values(Path(__file__).parents[2].joinpath(".env"))
# %%
# Set up a cache for citymapper requests to avoid going over the 5000/month limit
session = CachedSession(
    "traveltime_cache",
    use_cache_dir=True,                # Save files in the default user cache dir
    # Otherwise expire responses after one day
    expire_after=timedelta(days=100),
    # Cache POST requests to avoid sending the same data twice
    allowable_methods=["GET", "POST"],
    # Cache 400 responses as a solemn reminder of your failures
    allowable_codes=[200, 400],
    # Don't match this param or save it in the cache
    ignored_parameters=["TRAVELTIME_KEY"],
    match_headers=True,                # Match all request headers
    # In case of request errors, use stale cache data if possible
    stale_if_error=False,
)


class APIError(Exception):
    pass


# def get_api_headers() -> dict:

#     ttid = env.get('TRAVELTIME_ID', None)
#     ttkey = env.get('TRAVELTIME_KEY', None)

#     if ttid is None:
#         raise APIError(
#             "Please set env var TRAVELTIME_ID to your Travel Time Application Id")
#     if ttkey is None:
#         raise APIError(
#             "Please set env var TRAVELTIME_KEY to your Travel Time Api Key")

#     return {'X-Application-Id': ttid, 'X-Api-Key': ttkey, "Accept": "application/json"}


def traveltime_api(path: str, body: Optional[dict] = None, query: Optional[dict] = None) -> dict:

    url = f"https://1k8cheq5kc.execute-api.eu-west-2.amazonaws.com/{path}"

    if body is None:
        resp = session.get(url=url, params=query)
    else:
        resp = session.post(url=url, json=body)

    try:
        parsed = resp.json()
    except:
        raise APIError('Travel Time API did not return json') from None

    if resp.status_code != 200:
        msg = (
            f"Travel Time API request failed [{resp.status_code}]\n"
            f"{parsed['description']}\n"
            f"Error code: {parsed['error_code']}\n"
            f"<{parsed['documentation_link']}>\n"
        )
        if 'additional_info' in parsed:
            for k, v in parsed['additional_info'].items():
                msg += k + ": " + str(v) + "\n"

        raise APIError(msg)

    return parsed


def time_map(request: TimeMapRequest):
    """Isochrones (Time Map)
    Given origin coordinates, find shapes of zones reachable within corresponding travel time.
    Find unions/intersections between different searches
    See https://traveltime.com/docs/api/reference/isochrones for details
    Args:
        departure_searches (dict, optional): Searches based on departure times.
         Leave departure location at no earlier than given time. You can define a maximum of 10 searches
        arrival_searches (dict, optional): Searches based on arrival times.
         Arrive at destination location at no later than given time. You can define a maximum of 10 searches
        unions (dict, optional): Define unions of shapes that are results of previously defined searches.
        intersections (dict, optional): Define intersections of shapes that are results of previously defined searches.
    Returns:
         dict: API response parsed as a dictionary
    """
    if request.departure_searches is None and request.arrival_searches is None:
        raise ValueError(
            "At least one of arrival_searches/departure_searches required!"
        )

    return traveltime_api(
        path="time-map",
        body=request.dict(by_alias=True, exclude_unset=True, exclude_none=True)
    )


def postcode_search(request: TimeMapRequest):
    """
    """
    if request.departure_searches is None and request.arrival_searches is None:
        raise ValueError(
            "At least one of arrival_searches/departure_searches required!"
        )

    return traveltime_api(
        path="time-filter/postcodes",
        body=request.dict(by_alias=True, exclude_unset=True, exclude_none=True)
    )

