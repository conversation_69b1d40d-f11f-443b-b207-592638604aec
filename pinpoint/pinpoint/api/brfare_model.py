from enum import Enum
from pydantic import BaseModel, Field
from typing import Any, List, Optional


class Railcard(BaseModel):
    code: str
    name: str
    physical_card: bool
    geographically_restricted: bool
    restricted_issue: bool
    must_be_shown: bool
    holder: str
    min_pass: int
    max_pass: int
    min_holder: int
    max_holder: int
    min_accomp_adult: int
    max_accomp_adult: int
    min_adult: int
    max_adult: int
    min_child: int
    max_child: int
    price: int
    discount_price: int
    validity: int


class FareSetter(BaseModel):
    code: str
    name: str


class Route(BaseModel):
    code: str
    name: str
    longname: str
    ticketname: str


class Validity(BaseModel):
    """Ticket Validity, i.e. period within which ticket is valid to make a journey"""
    code: int = Field(
        description="Internal fares database code for this set of validity information"
    )
    desc: str = Field(description="Validity information relating to entire journey")
    out: str = Field(description="Short summary of validity period for outward journey")
    return_: str = Field(
        alias="rtn",
        description="Short summary of validity period for return journey"
    )
    outward_breakable: bool = Field(
        alias="out_boj",
        description="Outward journey may be broken at an intermediate station"
    )
    return_breakable: bool = Field(
        alias="rtn_boj",
        description="Outward journey may be broken at an intermediate station"
    )
    out_validity: int = Field(
        description=(
            "Period available (from the ticket date, inclusive) to make the outward "
            "journey in form (months x 1000) + days"
        )
    )
    rtn_validity: int = Field(
        description=(
            "Period available (from the ticket date, inclusive) to make the return "
            "journey in form (months x 1000) + days"
        )
    )
    min_stay: int = Field(
        description=(
            "Number of midnights that must pass between the outward and return journeys"
        )
    )
    rtn_after: int = Field(
        description=(
            "Day of week that must pass (starting from the ticket date, inclusive), "
            "before the return journey can be made. 0 = Sunday, 1 = Monday etc. -1 if "
            "this restriction does not apply."
        )
    )


class Location(BaseModel):
    national_location_code: Optional[str] = Field(alias="nlc")
    computer_reservation_system_code: Optional[str] = Field(alias="crs")
    name: Optional[str]
    longname: Optional[str]
    ticketname: Optional[str]
    code: Optional[str] = Field(
        title="Canonical Location Code",
        description=(
            "Canonical code to describe this location, e.g. in public-facing URLs. "
            "Generally will consist of the CRS code if available and unique, otherwise "
            "NLC."
        )
    )


class DataSource(BaseModel):
    code: int
    desc: str


class StrCodeDesc(BaseModel):
    code: str
    desc: str

class IntCodeDesc(BaseModel):
    code: int
    desc: str

class PassengerStatus(BaseModel):
    """Passenger status, derived from passenger type (Adult/Child/AAA) in combination
    with railcard or discount
    """
    code: str
    name: str
    ticket_code: str


class FareStatusPrice(BaseModel):
    """Fare status and price (N.B. may be an empty object in the event that a price is
    not available for the relevant passenger type)
    """
    status: Optional[PassengerStatus]
    fare: Optional[int] = Field(description="Price of the fare in pence")
    min_fare: Optional[int] = Field(
        description=(
            "Minimum price of the fare (in pence), which may apply at certain times as "
            "determined by any restriction codes"
        )
    )


class TicketTypeCode(int, Enum):
    single = 0
    return_ = 1
    season = 2

class TicketTypeDescription(str, Enum):
    single = "SINGLE"
    return_ = "RETURN"
    season = "SEASON"

class TicketType(BaseModel):
    code: TicketTypeCode = Field(description="Internal Code (0 = single, 1 = return, 2 = season)")
    desc: TicketTypeDescription

class AccommodationClassCode(int, Enum):
    first = 0
    standard = 1
    classless = 2

class AccommodationClassDescription(str, Enum):
    first = "1ST"
    standard = "STD"
    classless = " - "

class AccommodationClass(BaseModel):
    """Accommodation class of ticket"""
    code: AccommodationClassCode = Field(description="Internal code (0 = 1st Class, 1 = Standard, 2 = Classless)")
    desc: AccommodationClassDescription

class Ticket(BaseModel):
    code: str
    name: str
    longname: str
    type_: TicketType = Field(..., alias="type")
    accommodation_class: AccommodationClass = Field(alias="tclass")
    validity: Validity
    min_validity: int = Field(
        description=(
            "For season tickets only, minimum validity period in the form "
            "(months x 1000) + days. Originally from the fares mainframe, no longer "
            "reliably populated."
        )
    )
    max_validity: int = Field(
        description=(
            "For season tickets only, maximum validity period in the form "
            "(months x 1000) + days. Originally from the fares mainframe, no longer "
            "reliably populated."
        )
    )
    restrictions_by_date: bool = Field(
        alias="restr_date",
        description=(
            "May indicate that ticket has date-based availability restrictions. May "
            "not be accurate."
        )
    )
    restrictions_by_operator: bool = Field(
        alias="restr_train",
        description=(
            "May indicate that ticket has TOC (operator)-based availability "
            "restrictions. May not be accurate."
        )
    )
    restrictions_by_area: bool = Field(
        alias="restr_area",
        description=(
            "May indicate that ticket has restrictions on which route codes it may be "
            "used in combination with. Probably not accurate."
        )
    )
    reservations: IntCodeDesc


class Fare(BaseModel):
    category: StrCodeDesc
    flow_orig: Location
    flow_dest: Location
    group_orig: Location
    group_dest: Location
    travelcard_orig: bool
    travelcard_dest: bool
    zonal_orig: bool
    zonal_dest: bool
    fare_setter: FareSetter
    route: Route
    london_code: StrCodeDesc
    ticket: Ticket
    restriction_code: str
    railcard_restrictions: List[Any]
    reversible: bool
    adult: FareStatusPrice
    child: FareStatusPrice


class FareResult(BaseModel):
    orig: Location
    dest: Location
    travelcard_orig: bool
    travelcard_dest: bool
    zonal_orig: bool
    zonal_dest: bool
    railcard: Railcard
    railcard_valid: bool
    fares: List[Fare]
    source: DataSource
    valid_date: int
    valid_until_date: int
