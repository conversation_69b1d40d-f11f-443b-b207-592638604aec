from __future__ import annotations

from typing import List, Optional

from pydantic import BaseModel


class Coordinates(BaseModel):
    lat: float
    lon: float


class Start(BaseModel):
    coordinates: Coordinates


class End(BaseModel):
    coordinates: Coordinates


class Departure(BaseModel):
    type: str
    service_id: str
    headsign: Optional[str] = None
    scheduled_time: Optional[str] = None
    time_status: str
    frequency_seconds_range: Optional[List[int]] = None


class Status(BaseModel):
    type: str
    service_ids: List[str]


class UpdatableDetail(BaseModel):
    leg_departure_time: str
    leg_arrival_time: str
    departures: Optional[List[Departure]] = None
    live_departure_availability: Optional[str] = None
    statuses: Optional[List[Status]] = None


class RecommendedExit(BaseModel):
    id: str
    stop_id: str
    coordinates: Coordinates
    name: Optional[str] = None


class AlternateExit(BaseModel):
    id: str
    stop_id: str
    coordinates: Coordinates
    name: Optional[str] = None


class WalkDetailsEnterStation(BaseModel):
    recommended_exit: Optional[RecommendedExit] = None
    alternate_exits: Optional[List[AlternateExit]] = None
    duration_seconds: int


class Stop(BaseModel):
    id: str
    name: str
    coordinates: Coordinates
    indicator_text: Optional[str] = None


class Image(BaseModel):
    url: str
    width: int
    height: int
    ui_role: str
    is_generic: Optional[bool] = None
    has_space_for_text: Optional[bool] = None


class Brand(BaseModel):
    id: str
    name: str
    images: List[Image]


class Image(BaseModel):
    url: str
    width: int
    height: int
    ui_role: str


class Service(BaseModel):
    id: str
    name: str
    vehicle_types: List[str]
    brand: Brand
    images: Optional[List[Image]] = None
    color: str
    background_color: str
    text_color: Optional[str] = None


class BestBoardingSections(BaseModel):
    front: bool
    middle: bool
    back: bool


class Exit(BaseModel):
    id: str
    stop_id: str
    coordinates: Coordinates
    name: Optional[str] = None


class WalkDetailsExitStation(BaseModel):
    recommended_exit: Optional[Exit] = None
    duration_seconds: int
    alternate_exits: Optional[List[Exit]] = None


class Leg(BaseModel):
    travel_mode: str
    duration_seconds: int
    path: str
    updatable_detail: UpdatableDetail
    station_walk_type: Optional[str] = None
    walk_details_enter_station: Optional[WalkDetailsEnterStation] = None
    stops: Optional[List[Stop]] = None
    vehicle_types: Optional[List[str]] = None
    services: Optional[List[Service]] = None
    best_boarding_sections: Optional[BestBoardingSections] = None
    direction_description: Optional[str] = None
    walk_details_exit_station: Optional[WalkDetailsExitStation] = None


class Route(BaseModel):
    start: Start
    end: End
    duration_seconds: int
    duration_accuracy: str
    legs: List[Leg]
    route_departure_time: str
    route_arrival_time: str
    signature: str


class TransitDirections(BaseModel):
    routes: List[Route]
