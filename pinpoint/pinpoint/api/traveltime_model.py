from __future__ import annotations

from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class Coordinates(BaseModel):
    lat: float
    lng: float


class MaxChanges(BaseModel):
    enabled: bool
    limit: int


class Transportation(BaseModel):
    type_: str = Field(alias="type")
    disable_border_crossing: Optional[bool]
    pt_change_delay: Optional[int]
    max_changes: Optional[MaxChanges]
    walking_time: Optional[int] = Field(description="seconds")
    driving_time_to_station: Optional[int] = Field(description="seconds")
    cycling_time_to_station: Optional[int] = Field(description="seconds")
    parking_time: Optional[int] = Field(description="seconds")
    boarding_time: Optional[int] = Field(description="seconds")


class SearchRange(BaseModel):
    """Search range width in seconds.
    width along with departure_time specify departure interval.
    For example, if you set departure_time to 9am and width to 1 hour,
    we will return a combined shape of all possible journeys that have
    departure time between 9am and 10am.
    Range width is limited to 12 hours
    """
    enabled: bool
    width: int


class LevelOfDetail(BaseModel):
    scale_type: str
    level: Union[str, int]
    square_size: int


class DepartureSearch(BaseModel):
    id: str
    coords: Coordinates
    transportation: Transportation
    travel_time: int = Field(description="seconds")
    departure_time: str = Field(description="ISO 8601 timestamp")
    range_: Optional[SearchRange] = Field(alias="range")
    properties: Optional[List[str]]
    level_of_detail: Optional[LevelOfDetail]


class ArrivalSearch(BaseModel):
    id: str
    coords: Coordinates
    transportation: Transportation
    travel_time: int = Field(description="seconds")
    arrival_time: str = Field(description="ISO 8601 timestamp")
    range_: Optional[SearchRange] = Field(alias="range")
    properties: Optional[List[str]]
    level_of_detail: Optional[LevelOfDetail]


class Union(BaseModel):
    id: str
    search_ids: List[str]


class Intersection(BaseModel):
    id: str
    search_ids: List[str]


class TimeMapRequest(BaseModel):
    departure_searches: Optional[List[DepartureSearch]]
    arrival_searches: Optional[List[ArrivalSearch]]
    unions: Optional[List[Union]]
    intersections: Optional[List[Intersection]]


class Shape(BaseModel):
    shell: List[Coordinates]
    holes: List


class TimeMapResult(BaseModel):
    search_id: str
    shapes: List[Shape]
    properties: Dict[str, Any]


class TimeMapResponse(BaseModel):
    results: List[TimeMapResult]


class PostCodeProperty(BaseModel):
    travel_time: Optional[int] = None
    distance: Optional[int] = None


class PostCodeResult(BaseModel):
    code: str
    properties: List[PostCodeProperty]


class TravelTimeMatrixResult(BaseModel):
    search_id: str
    postcodes: List[PostCodeResult]


class TravelTimeMatrixResponse(BaseModel):
    results: List[TravelTimeMatrixResult]


"""
example = {
  "departure_searches": [
    {
      "id": "public transport from Trafalgar Square",
      "coords": {
        "lat": 51.507609,
        "lng": -0.128315
      },
      "transportation": {
        "type": "public_transport"
      },
      "departure_time": "2022-04-24T08:00:00Z",
      "travel_time": 900
    }
  ],
  "arrival_searches": [
    {
      "id": "public transport to Trafalgar Square",
      "coords": {
        "lat": 51.507609,
        "lng": -0.128315
      },
      "transportation": {
        "type": "public_transport",
        "max_changes": {
          "enabled": True,
          "limit": 7
        }
      },
      "arrival_time": "2022-04-24T08:00:00Z",
      "travel_time": 900,
      "range": {
        "enabled": True,
        "width": 3600
      }
    }
  ]
}

x = TimeMapRequest(**example)
reg = x.json(exclude_unset=True, exclude_none=True)
"""
