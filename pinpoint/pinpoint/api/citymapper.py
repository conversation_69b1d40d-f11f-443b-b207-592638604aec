# %%
from typing import Sequence, Tuple
from requests.auth import HTTPBasicAuth
import requests
import json
from devtools import debug
from datetime import timedelta
from requests_cache import CachedSession
from pinpoint.api.brfare import brfare_query, cheapest_year_season_price
from pinpoint.api.citymapper_model import Route, Stop, TransitDirections
from dotenv import dotenv_values

env = dotenv_values(".env")
# %%
# Set up a cache for citymapper requests to avoid going over the 5000/month limit
session = CachedSession(
    "citymapper_cache",
    use_cache_dir=True,                # Save files in the default user cache dir
    # Otherwise expire responses after one day
    expire_after=timedelta(days=100),
    # Cache POST requests to avoid sending the same data twice
    allowable_methods=["GET", "POST"],
    # Cache 400 responses as a solemn reminder of your failures
    allowable_codes=[200, 400],
    # Don't match this param or save it in the cache
    ignored_parameters=["Citymapper-Partner-Key"],
    match_headers=True,                # Match all request headers
    # In case of request errors, use stale cache data if possible
    stale_if_error=False,
)


def first_to_last_transit(route: Route) -> Tuple[Stop, Stop]:
    entrance_stop = None
    exit_stop = None
    for leg in route.legs:
        if (
            leg.travel_mode == "transit"
            and (
                "metro" in leg.vehicle_types
                or "rail" in leg.vehicle_types
            )
        ):
            if leg.stops is None:
                continue
            if entrance_stop is None:
                entrance_stop = leg.stops[0]
            exit_stop = leg.stops[-1]
    return (entrance_stop, exit_stop)

url = "https://api.external.citymapper.com/api/1/directions/transit"
headers = {
    "Accept": "application/json",
    "Citymapper-Partner-Key": env.get("CITYMAPPER-PARTNER-KEY")
}

def citymapper_query(params: dict) -> TransitDirections:
    response = session.get(url, params=params, headers=headers)

    result_raw = response.json()
    return TransitDirections(**result_raw)