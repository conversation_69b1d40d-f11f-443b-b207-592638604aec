# %%
from datetime import timedelta
from math import ceil
from typing import List

from pinpoint.api.brfare_model import (
    AccommodationClassCode,
    Fare,
    FareResult,
    TicketTypeCode
)
from requests_cache import CachedSession

# Set up a cache to avoid going over the monthly limit
session = CachedSession(
    "brfares_cache",
    use_cache_dir=True,                # Save files in the default user cache dir
    expire_after=timedelta(days=100),    # Otherwise expire responses after one day
    allowable_methods=["GET", "POST"], # Cache POST requests to avoid sending the same data twice
    allowable_codes=[200, 400],        # Cache 400 responses as a solemn reminder of your failures
    ignored_parameters=["Citymapper-Partner-Key"],    # Don't match this param or save it in the cache
    match_headers=True,                # Match all request headers
    stale_if_error=False,               # In case of request errors, use stale cache data if possible
)

url = "http://api.brfares.com/querysimple"
headers = {"Accept": "application/json"}


fix_names = {
    "Chiswick Park": "Chiswick Pk"
}

def brfare_query(origin: str, destination: str) -> FareResult:
    if origin in fix_names:
        origin = fix_names[origin]
    if destination in fix_names:
        destination = fix_names[destination]

    if (
        not (isinstance(origin, str) and isinstance(destination, str))
        or not origin or not destination
    ):
        raise ValueError(f"Origin or destination must be nonempty string, got `{origin}` and `{destination}`")
    params = {
        "orig": origin,
        "dest": destination
    }
    response = session.get(url, params=params, headers=headers)
    if response.status_code != 200:
        raise ValueError(
            f"Query returned bad status for `{origin}` to `{destination}`, "
            f"may be a station name mismatch. Response:\n{response}"
        )

    result_raw = response.json()
    return FareResult(**result_raw)


def filter_season(fare_result: FareResult) -> List[Fare]:
    return [
        fare for fare in fare_result.fares
        if fare.ticket.type_.code == TicketTypeCode.season
        and fare.ticket.accommodation_class.code in (
            AccommodationClassCode.standard,
            AccommodationClassCode.classless
        )
        and fare.adult.status
    ]


def season_fare(base_pence:float, months:int=0, days:int=0):
    mult = 3.84 * min(months, 10) + 0.64 * (days//5) + 0.13 * (days % 5)
    result = mult * base_pence

    if months == 0:
        if days != 7:
            raise ValueError(f"Season tickets are not available for {days} days - must be a 7 day season, or over 1 month, up to 1 year")
        result = base_pence

    if months > 12:
        raise NotImplementedError("Season fares for more than 12 months not implemented")

    if months == 10 and days > 13 or months > 10:
        result = 40*base_pence
    return ceil(result/10)*10


def cheapest_year_season_price(fare_result: FareResult) -> float:
    target_fares = filter_season(fare_result)

    cheapest_fare: Fare = min(target_fares, key=lambda fare: fare.adult.fare)
    year_season_pence = season_fare(cheapest_fare.adult.fare, months=12)

    return year_season_pence
