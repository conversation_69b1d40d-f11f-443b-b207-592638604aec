from dataclasses import dataclass
from typing import Generator
from pinpoint.api.brfare import brfare_query, cheapest_year_season_price
from pinpoint.api.brfare_model import FareResult
from pinpoint.api.citymapper import citymapper_query, first_to_last_transit
from pinpoint.api.citymapper_model import Stop, Route


@dataclass
class RoutePriceTime:
    origin: Stop
    destination: Stop
    route: Route
    fare: FareResult
    year_season_pence: float

def get_routes_and_ticket_prices(start_end_params: dict) -> (
    Generator[RoutePriceTime, None, None]
):

    result = citymapper_query(params)
    for route in result.routes:
        origin, destination = first_to_last_transit(route)
        fare = brfare_query(origin=origin.name, destination=destination.name)

        year_season_pence = cheapest_year_season_price(fare)
        yield RoutePriceTime(
            origin=origin,
            destination=destination, 
            route=route,
            fare=fare,
            year_season_pence=year_season_pence
        )

params = {
    "start": "52.27941113889906,-1.5416574552499274",
    "end": "52.239698098596314,-0.8890229877664148"
}

def main():
    for result in get_routes_and_ticket_prices(params):
        print(
            f"From {result.origin.name} to {result.destination.name}.\n"
            f"Time {result.route.duration_seconds/60:.0f} minutes.\n"
            f"Cheapest year season fare: £{result.year_season_pence/100}.\n"
        )

if __name__ == "__main__":
    main()
