<!DOCTYPE qgis PUBLIC 'http://mrcc.com/qgis.dtd' 'SYSTEM'>
<qgis projectname="" version="2.2.0-Valmiera">
    <title></title>
    <relations>
        <relation referencingLayer="airports20140502155725153" referencedLayer="regions20140502145258447" id="airport_region_fk" name="airport_regions">
            <fieldRef referencedField="ID" referencingField="fk_region"/>
        </relation>
    </relations>
    <mapcanvas>
        <units>feet</units>
        <extent>
            <xmin>1097910.78085389896295965</xmin>
            <ymin>4838855.41812700964510441</ymin>
            <xmax>2303327.5099564790725708</xmax>
            <ymax>5661673.33837485127151012</ymax>
        </extent>
        <projections>0</projections>
        <destinationsrs>
            <spatialrefsys>
                <proj4>+proj=aea +lat_1=55 +lat_2=65 +lat_0=50 +lon_0=-154 +x_0=0 +y_0=0 +datum=NAD27 +units=us-ft +no_defs</proj4>
                <srsid>932</srsid>
                <srid>2964</srid>
                <authid>EPSG:2964</authid>
                <description>NAD27 / Alaska Albers</description>
                <projectionacronym>aea</projectionacronym>
                <ellipsoidacronym>clrk66</ellipsoidacronym>
                <geographicflag>false</geographicflag>
            </spatialrefsys>
        </destinationsrs>
        <layer_coordinate_transform_info/>
    </mapcanvas>
    <legend updateDrawingOrder="true" activeLayer="airports20140502155725153">
        <legendlayer drawingOrder="-1" open="true" checked="Qt::Checked" name="airports" showFeatureCount="0">
            <filegroup open="true" hidden="false">
                <legendlayerfile isInOverview="0" layerid="airports20140502155725153" visible="1"/>
            </filegroup>
        </legendlayer>
        <legendlayer drawingOrder="-1" open="true" checked="Qt::Checked" name="regions" showFeatureCount="0">
            <filegroup open="true" hidden="false">
                <legendlayerfile isInOverview="0" layerid="regions20140502145258447" visible="1"/>
            </filegroup>
        </legendlayer>
    </legend>
    <projectlayers layercount="2">
        <maplayer minimumScale="-4.65661e-10" maximumScale="1e+08" simplifyDrawingHints="0" minLabelScale="0" maxLabelScale="1e+08" simplifyDrawingTol="1" geometry="Point" simplifyMaxScale="1" type="vector" hasScaleBasedVisibilityFlag="0" simplifyLocal="1" scaleBasedLabelVisibilityFlag="0">
            <id>airports20140502155725153</id>
            <datasource>../shapefiles/airports.shp</datasource>
            <title></title>
            <abstract></abstract>
            <keywordList>
                <value></value>
            </keywordList>
            <layername>airports</layername>
            <srs>
                <spatialrefsys>
                    <proj4>+proj=aea +lat_1=55 +lat_2=65 +lat_0=50 +lon_0=-154 +x_0=0 +y_0=0 +datum=NAD27 +units=us-ft +no_defs</proj4>
                    <srsid>932</srsid>
                    <srid>2964</srid>
                    <authid>EPSG:2964</authid>
                    <description>NAD27 / Alaska Albers</description>
                    <projectionacronym>aea</projectionacronym>
                    <ellipsoidacronym>clrk66</ellipsoidacronym>
                    <geographicflag>false</geographicflag>
                </spatialrefsys>
            </srs>
            <provider encoding="System">ogr</provider>
            <previewExpression>COALESCE( "NAME", '&lt;NULL>' )</previewExpression>
            <vectorjoins/>
            <renderer-v2 symbollevels="0" type="singleSymbol">
                <symbols>
                    <symbol alpha="1" type="marker" name="0">
                        <layer pass="0" class="SimpleMarker" locked="0">
                            <prop k="angle" v="0"/>
                            <prop k="color" v="239,69,7,255"/>
                            <prop k="color_border" v="0,0,0,255"/>
                            <prop k="horizontal_anchor_point" v="1"/>
                            <prop k="name" v="circle"/>
                            <prop k="offset" v="0,0"/>
                            <prop k="offset_unit" v="MM"/>
                            <prop k="outline_style" v="solid"/>
                            <prop k="outline_width" v="0"/>
                            <prop k="outline_width_unit" v="MM"/>
                            <prop k="scale_method" v="area"/>
                            <prop k="size" v="2"/>
                            <prop k="size_unit" v="MM"/>
                            <prop k="vertical_anchor_point" v="1"/>
                        </layer>
                    </symbol>
                </symbols>
                <rotation/>
                <sizescale scalemethod="area"/>
            </renderer-v2>
            <customproperties>
                <property key="labeling" value="pal"/>
                <property key="labeling/addDirectionSymbol" value="false"/>
                <property key="labeling/angleOffset" value="0"/>
                <property key="labeling/blendMode" value="0"/>
                <property key="labeling/bufferBlendMode" value="0"/>
                <property key="labeling/bufferColorA" value="255"/>
                <property key="labeling/bufferColorB" value="255"/>
                <property key="labeling/bufferColorG" value="255"/>
                <property key="labeling/bufferColorR" value="255"/>
                <property key="labeling/bufferDraw" value="true"/>
                <property key="labeling/bufferJoinStyle" value="64"/>
                <property key="labeling/bufferNoFill" value="false"/>
                <property key="labeling/bufferSize" value="1"/>
                <property key="labeling/bufferSizeInMapUnits" value="false"/>
                <property key="labeling/bufferTransp" value="0"/>
                <property key="labeling/centroidWhole" value="false"/>
                <property key="labeling/decimals" value="3"/>
                <property key="labeling/displayAll" value="false"/>
                <property key="labeling/dist" value="1"/>
                <property key="labeling/distInMapUnits" value="false"/>
                <property key="labeling/enabled" value="true"/>
                <property key="labeling/fieldName" value="NAME"/>
                <property key="labeling/fontBold" value="false"/>
                <property key="labeling/fontCapitals" value="0"/>
                <property key="labeling/fontFamily" value="Sans Serif"/>
                <property key="labeling/fontItalic" value="false"/>
                <property key="labeling/fontLetterSpacing" value="0"/>
                <property key="labeling/fontLimitPixelSize" value="false"/>
                <property key="labeling/fontMaxPixelSize" value="10000"/>
                <property key="labeling/fontMinPixelSize" value="3"/>
                <property key="labeling/fontSize" value="9"/>
                <property key="labeling/fontSizeInMapUnits" value="false"/>
                <property key="labeling/fontStrikeout" value="false"/>
                <property key="labeling/fontUnderline" value="false"/>
                <property key="labeling/fontWeight" value="50"/>
                <property key="labeling/fontWordSpacing" value="0"/>
                <property key="labeling/formatNumbers" value="false"/>
                <property key="labeling/isExpression" value="false"/>
                <property key="labeling/labelOffsetInMapUnits" value="true"/>
                <property key="labeling/labelPerPart" value="false"/>
                <property key="labeling/leftDirectionSymbol" value="&lt;"/>
                <property key="labeling/limitNumLabels" value="false"/>
                <property key="labeling/maxCurvedCharAngleIn" value="20"/>
                <property key="labeling/maxCurvedCharAngleOut" value="-20"/>
                <property key="labeling/maxNumLabels" value="2000"/>
                <property key="labeling/mergeLines" value="false"/>
                <property key="labeling/minFeatureSize" value="0"/>
                <property key="labeling/multilineAlign" value="0"/>
                <property key="labeling/multilineHeight" value="1"/>
                <property key="labeling/namedStyle" value="Normal"/>
                <property key="labeling/obstacle" value="true"/>
                <property key="labeling/placeDirectionSymbol" value="0"/>
                <property key="labeling/placement" value="0"/>
                <property key="labeling/placementFlags" value="0"/>
                <property key="labeling/plussign" value="false"/>
                <property key="labeling/preserveRotation" value="true"/>
                <property key="labeling/previewBkgrdColor" value="#ffffff"/>
                <property key="labeling/priority" value="5"/>
                <property key="labeling/quadOffset" value="4"/>
                <property key="labeling/reverseDirectionSymbol" value="false"/>
                <property key="labeling/rightDirectionSymbol" value=">"/>
                <property key="labeling/scaleMax" value="10000000"/>
                <property key="labeling/scaleMin" value="1"/>
                <property key="labeling/scaleVisibility" value="false"/>
                <property key="labeling/shadowBlendMode" value="6"/>
                <property key="labeling/shadowColorB" value="0"/>
                <property key="labeling/shadowColorG" value="0"/>
                <property key="labeling/shadowColorR" value="0"/>
                <property key="labeling/shadowDraw" value="false"/>
                <property key="labeling/shadowOffsetAngle" value="135"/>
                <property key="labeling/shadowOffsetDist" value="1"/>
                <property key="labeling/shadowOffsetGlobal" value="true"/>
                <property key="labeling/shadowOffsetUnits" value="1"/>
                <property key="labeling/shadowRadius" value="1.5"/>
                <property key="labeling/shadowRadiusAlphaOnly" value="false"/>
                <property key="labeling/shadowRadiusUnits" value="1"/>
                <property key="labeling/shadowScale" value="100"/>
                <property key="labeling/shadowTransparency" value="30"/>
                <property key="labeling/shadowUnder" value="0"/>
                <property key="labeling/shapeBlendMode" value="0"/>
                <property key="labeling/shapeBorderColorA" value="255"/>
                <property key="labeling/shapeBorderColorB" value="128"/>
                <property key="labeling/shapeBorderColorG" value="128"/>
                <property key="labeling/shapeBorderColorR" value="128"/>
                <property key="labeling/shapeBorderWidth" value="0"/>
                <property key="labeling/shapeBorderWidthUnits" value="1"/>
                <property key="labeling/shapeDraw" value="false"/>
                <property key="labeling/shapeFillColorA" value="255"/>
                <property key="labeling/shapeFillColorB" value="255"/>
                <property key="labeling/shapeFillColorG" value="255"/>
                <property key="labeling/shapeFillColorR" value="255"/>
                <property key="labeling/shapeJoinStyle" value="64"/>
                <property key="labeling/shapeOffsetUnits" value="1"/>
                <property key="labeling/shapeOffsetX" value="0"/>
                <property key="labeling/shapeOffsetY" value="0"/>
                <property key="labeling/shapeRadiiUnits" value="1"/>
                <property key="labeling/shapeRadiiX" value="0"/>
                <property key="labeling/shapeRadiiY" value="0"/>
                <property key="labeling/shapeRotation" value="0"/>
                <property key="labeling/shapeRotationType" value="0"/>
                <property key="labeling/shapeSVGFile" value=""/>
                <property key="labeling/shapeSizeType" value="0"/>
                <property key="labeling/shapeSizeUnits" value="1"/>
                <property key="labeling/shapeSizeX" value="0"/>
                <property key="labeling/shapeSizeY" value="0"/>
                <property key="labeling/shapeTransparency" value="0"/>
                <property key="labeling/shapeType" value="0"/>
                <property key="labeling/textColorA" value="255"/>
                <property key="labeling/textColorB" value="0"/>
                <property key="labeling/textColorG" value="0"/>
                <property key="labeling/textColorR" value="255"/>
                <property key="labeling/textTransp" value="0"/>
                <property key="labeling/upsidedownLabels" value="0"/>
                <property key="labeling/wrapChar" value=""/>
                <property key="labeling/xOffset" value="0"/>
                <property key="labeling/yOffset" value="0"/>
            </customproperties>
            <blendMode>0</blendMode>
            <featureBlendMode>0</featureBlendMode>
            <layerTransparency>0</layerTransparency>
            <displayfield>NAME</displayfield>
            <label>0</label>
            <labelattributes>
                <label fieldname="" text="Label"/>
                <family fieldname="" name="Sans Serif"/>
                <size fieldname="" units="pt" value="12"/>
                <bold fieldname="" on="0"/>
                <italic fieldname="" on="0"/>
                <underline fieldname="" on="0"/>
                <strikeout fieldname="" on="0"/>
                <color fieldname="" red="0" blue="0" green="0"/>
                <x fieldname=""/>
                <y fieldname=""/>
                <offset x="0" y="0" units="pt" yfieldname="" xfieldname=""/>
                <angle fieldname="" value="0" auto="0"/>
                <alignment fieldname="" value="center"/>
                <buffercolor fieldname="" red="255" blue="255" green="255"/>
                <buffersize fieldname="" units="pt" value="1"/>
                <bufferenabled fieldname="" on=""/>
                <multilineenabled fieldname="" on=""/>
                <selectedonly on=""/>
            </labelattributes>
            <edittypes>
                <edittype labelontop="0" editable="1" type="0" name="ELEV"/>
                <edittype labelontop="0" editable="1" type="0" name="ID"/>
                <edittype labelontop="0" editable="1" type="0" name="NAME"/>
                <edittype labelontop="0" editable="1" type="0" name="USE"/>
                <edittype labelontop="0" editable="1" widgetv2type="RelationReference" type="20" name="fk_region">
                    <widgetv2config ShowForm="0" Relation="airport_region_fk" AllowNULL="0"/>
                </edittype>
            </edittypes>
            <editform></editform>
            <editforminit></editforminit>
            <featformsuppress>0</featformsuppress>
            <annotationform></annotationform>
            <editorlayout>generatedlayout</editorlayout>
            <excludeAttributesWMS/>
            <excludeAttributesWFS/>
            <attributeactions/>
        </maplayer>
        <maplayer minimumScale="0" maximumScale="1e+08" simplifyDrawingHints="0" minLabelScale="0" maxLabelScale="1e+08" simplifyDrawingTol="1" geometry="Polygon" simplifyMaxScale="1" type="vector" hasScaleBasedVisibilityFlag="0" simplifyLocal="1" scaleBasedLabelVisibilityFlag="0">
            <id>regions20140502145258447</id>
            <datasource>../shapefiles/regions.shp</datasource>
            <title></title>
            <abstract></abstract>
            <keywordList>
                <value></value>
            </keywordList>
            <layername>regions</layername>
            <srs>
                <spatialrefsys>
                    <proj4>+proj=aea +lat_1=55 +lat_2=65 +lat_0=50 +lon_0=-154 +x_0=0 +y_0=0 +ellps=clrk66 +towgs84=-10,158,187,0,0,0,0 +units=us-ft +no_defs</proj4>
                    <srsid>100009</srsid>
                    <srid>0</srid>
                    <authid>USER:100009</authid>
                    <description> * Erzeugtes KBS (+proj=aea +lat_1=55 +lat_2=65 +lat_0=50 +lon_0=-154 +x_0=0 +y_0=0 +ellps=clrk66 +towgs84=-10,158,187,0,0,0,0 +units=us-ft +no_defs)</description>
                    <projectionacronym>aea</projectionacronym>
                    <ellipsoidacronym>clrk66</ellipsoidacronym>
                    <geographicflag>false</geographicflag>
                </spatialrefsys>
            </srs>
            <provider encoding="System">ogr</provider>
            <previewExpression></previewExpression>
            <vectorjoins/>
            <renderer-v2 symbollevels="0" type="singleSymbol">
                <symbols>
                    <symbol alpha="1" type="fill" name="0">
                        <layer pass="0" class="SimpleFill" locked="0">
                            <prop k="border_width_unit" v="MM"/>
                            <prop k="color" v="255,255,192,255"/>
                            <prop k="color_border" v="0,0,0,255"/>
                            <prop k="offset" v="0,0"/>
                            <prop k="offset_unit" v="MM"/>
                            <prop k="style" v="solid"/>
                            <prop k="style_border" v="solid"/>
                            <prop k="width_border" v="0.26"/>
                        </layer>
                    </symbol>
                </symbols>
                <rotation/>
                <sizescale scalemethod="area"/>
            </renderer-v2>
            <customproperties>
                <property key="labeling" value="pal"/>
                <property key="labeling/addDirectionSymbol" value="false"/>
                <property key="labeling/angleOffset" value="0"/>
                <property key="labeling/blendMode" value="0"/>
                <property key="labeling/bufferBlendMode" value="0"/>
                <property key="labeling/bufferColorA" value="255"/>
                <property key="labeling/bufferColorB" value="255"/>
                <property key="labeling/bufferColorG" value="255"/>
                <property key="labeling/bufferColorR" value="255"/>
                <property key="labeling/bufferDraw" value="false"/>
                <property key="labeling/bufferJoinStyle" value="64"/>
                <property key="labeling/bufferNoFill" value="false"/>
                <property key="labeling/bufferSize" value="1"/>
                <property key="labeling/bufferSizeInMapUnits" value="false"/>
                <property key="labeling/bufferTransp" value="0"/>
                <property key="labeling/centroidWhole" value="false"/>
                <property key="labeling/decimals" value="3"/>
                <property key="labeling/displayAll" value="false"/>
                <property key="labeling/dist" value="0"/>
                <property key="labeling/distInMapUnits" value="false"/>
                <property key="labeling/enabled" value="true"/>
                <property key="labeling/fieldName" value="NAME_2"/>
                <property key="labeling/fontBold" value="false"/>
                <property key="labeling/fontCapitals" value="0"/>
                <property key="labeling/fontFamily" value="Sans Serif"/>
                <property key="labeling/fontItalic" value="false"/>
                <property key="labeling/fontLetterSpacing" value="0"/>
                <property key="labeling/fontLimitPixelSize" value="false"/>
                <property key="labeling/fontMaxPixelSize" value="10000"/>
                <property key="labeling/fontMinPixelSize" value="3"/>
                <property key="labeling/fontSize" value="9"/>
                <property key="labeling/fontSizeInMapUnits" value="false"/>
                <property key="labeling/fontStrikeout" value="false"/>
                <property key="labeling/fontUnderline" value="false"/>
                <property key="labeling/fontWeight" value="50"/>
                <property key="labeling/fontWordSpacing" value="0"/>
                <property key="labeling/formatNumbers" value="false"/>
                <property key="labeling/isExpression" value="false"/>
                <property key="labeling/labelOffsetInMapUnits" value="true"/>
                <property key="labeling/labelPerPart" value="false"/>
                <property key="labeling/leftDirectionSymbol" value="&lt;"/>
                <property key="labeling/limitNumLabels" value="false"/>
                <property key="labeling/maxCurvedCharAngleIn" value="20"/>
                <property key="labeling/maxCurvedCharAngleOut" value="-20"/>
                <property key="labeling/maxNumLabels" value="2000"/>
                <property key="labeling/mergeLines" value="false"/>
                <property key="labeling/minFeatureSize" value="0"/>
                <property key="labeling/multilineAlign" value="0"/>
                <property key="labeling/multilineHeight" value="1"/>
                <property key="labeling/namedStyle" value="Normal"/>
                <property key="labeling/obstacle" value="true"/>
                <property key="labeling/placeDirectionSymbol" value="0"/>
                <property key="labeling/placement" value="0"/>
                <property key="labeling/placementFlags" value="0"/>
                <property key="labeling/plussign" value="false"/>
                <property key="labeling/preserveRotation" value="true"/>
                <property key="labeling/previewBkgrdColor" value="#ffffff"/>
                <property key="labeling/priority" value="5"/>
                <property key="labeling/quadOffset" value="4"/>
                <property key="labeling/reverseDirectionSymbol" value="false"/>
                <property key="labeling/rightDirectionSymbol" value=">"/>
                <property key="labeling/scaleMax" value="10000000"/>
                <property key="labeling/scaleMin" value="1"/>
                <property key="labeling/scaleVisibility" value="false"/>
                <property key="labeling/shadowBlendMode" value="6"/>
                <property key="labeling/shadowColorB" value="0"/>
                <property key="labeling/shadowColorG" value="0"/>
                <property key="labeling/shadowColorR" value="0"/>
                <property key="labeling/shadowDraw" value="false"/>
                <property key="labeling/shadowOffsetAngle" value="135"/>
                <property key="labeling/shadowOffsetDist" value="1"/>
                <property key="labeling/shadowOffsetGlobal" value="true"/>
                <property key="labeling/shadowOffsetUnits" value="1"/>
                <property key="labeling/shadowRadius" value="1.5"/>
                <property key="labeling/shadowRadiusAlphaOnly" value="false"/>
                <property key="labeling/shadowRadiusUnits" value="1"/>
                <property key="labeling/shadowScale" value="100"/>
                <property key="labeling/shadowTransparency" value="30"/>
                <property key="labeling/shadowUnder" value="0"/>
                <property key="labeling/shapeBlendMode" value="0"/>
                <property key="labeling/shapeBorderColorA" value="255"/>
                <property key="labeling/shapeBorderColorB" value="128"/>
                <property key="labeling/shapeBorderColorG" value="128"/>
                <property key="labeling/shapeBorderColorR" value="128"/>
                <property key="labeling/shapeBorderWidth" value="0"/>
                <property key="labeling/shapeBorderWidthUnits" value="1"/>
                <property key="labeling/shapeDraw" value="false"/>
                <property key="labeling/shapeFillColorA" value="255"/>
                <property key="labeling/shapeFillColorB" value="255"/>
                <property key="labeling/shapeFillColorG" value="255"/>
                <property key="labeling/shapeFillColorR" value="255"/>
                <property key="labeling/shapeJoinStyle" value="64"/>
                <property key="labeling/shapeOffsetUnits" value="1"/>
                <property key="labeling/shapeOffsetX" value="0"/>
                <property key="labeling/shapeOffsetY" value="0"/>
                <property key="labeling/shapeRadiiUnits" value="1"/>
                <property key="labeling/shapeRadiiX" value="0"/>
                <property key="labeling/shapeRadiiY" value="0"/>
                <property key="labeling/shapeRotation" value="0"/>
                <property key="labeling/shapeRotationType" value="0"/>
                <property key="labeling/shapeSVGFile" value=""/>
                <property key="labeling/shapeSizeType" value="0"/>
                <property key="labeling/shapeSizeUnits" value="1"/>
                <property key="labeling/shapeSizeX" value="0"/>
                <property key="labeling/shapeSizeY" value="0"/>
                <property key="labeling/shapeTransparency" value="0"/>
                <property key="labeling/shapeType" value="0"/>
                <property key="labeling/textColorA" value="255"/>
                <property key="labeling/textColorB" value="0"/>
                <property key="labeling/textColorG" value="0"/>
                <property key="labeling/textColorR" value="0"/>
                <property key="labeling/textTransp" value="0"/>
                <property key="labeling/upsidedownLabels" value="0"/>
                <property key="labeling/wrapChar" value=""/>
                <property key="labeling/xOffset" value="0"/>
                <property key="labeling/yOffset" value="0"/>
            </customproperties>
            <blendMode>0</blendMode>
            <featureBlendMode>0</featureBlendMode>
            <layerTransparency>0</layerTransparency>
            <displayfield>NAME_1</displayfield>
            <label>0</label>
            <labelattributes>
                <label fieldname="" text="Beschriftung"/>
                <family fieldname="" name="Sans Serif"/>
                <size fieldname="" units="pt" value="12"/>
                <bold fieldname="" on="0"/>
                <italic fieldname="" on="0"/>
                <underline fieldname="" on="0"/>
                <strikeout fieldname="" on="0"/>
                <color fieldname="" red="0" blue="0" green="0"/>
                <x fieldname=""/>
                <y fieldname=""/>
                <offset x="0" y="0" units="pt" yfieldname="" xfieldname=""/>
                <angle fieldname="" value="0" auto="0"/>
                <alignment fieldname="" value="center"/>
                <buffercolor fieldname="" red="255" blue="255" green="255"/>
                <buffersize fieldname="" units="pt" value="1"/>
                <bufferenabled fieldname="" on=""/>
                <multilineenabled fieldname="" on=""/>
                <selectedonly on=""/>
            </labelattributes>
            <edittypes>
                <edittype labelontop="0" editable="1" type="0" name="HASC_2"/>
                <edittype labelontop="0" editable="1" type="0" name="ID"/>
                <edittype labelontop="0" editable="1" type="0" name="NAME_1"/>
                <edittype labelontop="0" editable="1" type="0" name="NAME_2"/>
                <edittype labelontop="0" editable="1" type="0" name="TYPE_2"/>
            </edittypes>
            <editform>/home/<USER>/editform>
            <editforminit></editforminit>
            <featformsuppress>0</featformsuppress>
            <annotationform>/home/<USER>/annotationform>
            <editorlayout>generatedlayout</editorlayout>
            <excludeAttributesWMS/>
            <excludeAttributesWFS/>
            <attributeactions/>
        </maplayer>
    </projectlayers>
    <properties>
        <WMSContactPerson type="QString"></WMSContactPerson>
        <EventLayer>
            <OffsetScales type="QStringList"/>
            <FromFields type="QStringList"/>
            <MemoryLayers type="QStringList"/>
            <LineFields type="QStringList"/>
            <ToFields type="QStringList"/>
            <ForceSingleGeometries type="QStringList"/>
            <EventLayers type="QStringList"/>
            <LineLayers type="QStringList"/>
            <EventFields type="QStringList"/>
            <OffsetFields type="QStringList"/>
        </EventLayer>
        <WMSOnlineResource type="QString"></WMSOnlineResource>
        <WMSContactOrganization type="QString"></WMSContactOrganization>
        <WMSKeywordList type="QStringList">
            <value></value>
        </WMSKeywordList>
        <WFSUrl type="QString"></WFSUrl>
        <Paths>
            <Absolute type="bool">false</Absolute>
        </Paths>
        <WMSServiceTitle type="QString"></WMSServiceTitle>
        <WFSLayers type="QStringList"/>
        <WMSContactMail type="QString"></WMSContactMail>
        <PositionPrecision>
            <DecimalPlaces type="int">2</DecimalPlaces>
            <Automatic type="bool">true</Automatic>
            <DegreeFormat type="QString">D</DegreeFormat>
        </PositionPrecision>
        <WCSUrl type="QString"></WCSUrl>
        <WMSContactPhone type="QString"></WMSContactPhone>
        <WMSServiceCapabilities type="bool">false</WMSServiceCapabilities>
        <WMSServiceAbstract type="QString"></WMSServiceAbstract>
        <WMSAddWktGeometry type="bool">false</WMSAddWktGeometry>
        <Measure>
            <Ellipsoid type="QString">NONE</Ellipsoid>
        </Measure>
        <WFSTLayers>
            <Insert type="QStringList"/>
            <Update type="QStringList"/>
            <Delete type="QStringList"/>
        </WFSTLayers>
        <Gui>
            <SelectionColorBluePart type="int">0</SelectionColorBluePart>
            <CanvasColorGreenPart type="int">255</CanvasColorGreenPart>
            <CanvasColorRedPart type="int">255</CanvasColorRedPart>
            <SelectionColorRedPart type="int">255</SelectionColorRedPart>
            <SelectionColorAlphaPart type="int">255</SelectionColorAlphaPart>
            <SelectionColorGreenPart type="int">255</SelectionColorGreenPart>
            <CanvasColorBluePart type="int">255</CanvasColorBluePart>
        </Gui>
        <Identify>
            <disabledLayers type="QStringList"/>
        </Identify>
        <Macros>
            <pythonCode type="QString"></pythonCode>
        </Macros>
        <WMSAccessConstraints type="QString"></WMSAccessConstraints>
        <WCSLayers type="QStringList"/>
        <SpatialRefSys>
            <ProjectCRSProj4String type="QString">+proj=tmerc +lat_0=0 +lon_0=6 +k=1 +x_0=2500000 +y_0=0 +ellps=bessel +towgs84=598.1,73.7,418.2,0.202,0.045,-2.455,6.7 +units=m +no_defs</ProjectCRSProj4String>
            <ProjectCrs type="QString">EPSG:31466</ProjectCrs>
            <ProjectCRSID type="int">2646</ProjectCRSID>
        </SpatialRefSys>
        <DefaultStyles>
            <Fill type="QString"></Fill>
            <Line type="QString"></Line>
            <Marker type="QString"></Marker>
            <RandomColors type="bool">true</RandomColors>
            <AlphaInt type="int">255</AlphaInt>
            <ColorRamp type="QString"></ColorRamp>
        </DefaultStyles>
        <WMSFees type="QString"></WMSFees>
        <WMSUrl type="QString"></WMSUrl>
    </properties>
</qgis>
