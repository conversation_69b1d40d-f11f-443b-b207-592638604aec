mod feed_parsing;
mod parse_fare_toc;

use feed_parsing::parse_comment_block;
use nom::{
    character::complete::{char, line_ending, multispace0, none_of, one_of},
    combinator::{map, recognize, opt, value, all_consuming},
    multi::{many1, many_m_n, many0},
    sequence::{pair, terminated, tuple, preceded, delimited},
    IResult, branch::alt, Parser,
    bytes::complete::take, error::{context, convert_error},
};
use feed_parsing::parse_comment;
use parse_fare_toc::parse_toc;

fn main() {
    let input = "/!! Start of file                                                               
/!! Content type:  TOC                                                          
/!! Sequence:      648                                                          
/!! Records:       143                                                          
/!! Generated:     24/04/2023                                                   
/!! Exporter:      RJIS RjEhrFrs v 4.6.5                                        
FSWTSWSOUTH WESTERN RAILWAY         
FTAC  WATFORD TAAC FOR CSV FILE ONLY
FTGNGNTHAMESLINK AND GT NORTHERN GN 
FTLK  THAMESLINK                    
FTPETPTRANSPENNINE EXPRESS          
FWAB  WALES & BORDERS               
FWGN  WAGN                          
FWSM  WREXHAM SHROPSHIRE MARYLEBONE 
FWSX  WESSEX TRAINS                 
TAMARRIVA MERSEYRAIL                     N
TANARRIVA NORTHERNSPIRIT                 N
TARANGLIA RAILWAYS TRAIN SERVICES        N
TAWTRANSPORT FOR WALES RAIL              Y
TCCC2C                                   Y
TCHCHILTERN RAILWAYS                     Y
TCSSERCO CALEDONIAN SLEEPER              Y
/!! End of file (24/04/2023)
";


    match all_consuming(parse_toc).parse(input) {
        Ok((_, (toc_records, fare_records))) => {
            println!("Fare Records:");
            for record in toc_records {
                println!("{:?}", record);
            }
            println!("\nTOC Records:");
            for record in fare_records {
                println!("{:?}", record);
            }
        }
        Err(e) => println!("Error: {:?}", e),
    }

}
