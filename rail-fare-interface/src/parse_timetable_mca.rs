use crate::feed_parsing::{parse_comment_block, take_trim};
use nom::{
    character::complete::{char, line_ending, multispace0, none_of, one_of},
    combinator::{map, recognize, opt, value, all_consuming},
    multi::{many1, many_m_n, many0},
    sequence::{pair, terminated, tuple, preceded, delimited},
    IResult, branch::alt, Parser,
    bytes::complete::{take, tag, take_until}, error::{context, convert_error}, AsChar,
};
use crate::feed_parsing::parse_comment;
use std::str::FromStr;

#[derive(Debug)]
enum TransactionType{
    New,
    Delete,
    Revise
}
impl TransactionType {

    pub fn from(c: char) -> Result<TransactionType, ()> {
        match c {
            'D' => Ok(TransactionType::Delete),
            'N' => Ok(TransactionType::New),
            'R' => Ok(TransactionType::Revise),
            _ => Err(()),
        }
    }
}

#[derive(Debug)]
enum STPIndicator{
    Cancellation, // STP cancellation ofpermanent schedule.
    New, // New STP schedule.
    Overlay, // STP overlay of permanent schedule.
    Permanent,
}


impl STPIndicator {

    fn from(c: char) -> Result<STPIndicator, ()> {
        match c {
            'C' => Ok(STPIndicator::Cancellation),
            'N' => Ok(STPIndicator::New),
            'O' => Ok(STPIndicator::Overlay),
            'P' => Ok(STPIndicator::Permanent),
            _ => Err(()),
        }
    }
}

#[derive(Debug)]
pub struct BasicScheduleRecord<'a> {
    // BSNC002062305212312030000001 POO2A205320122323000 DMUS   075      S            P
    transaction_type: TransactionType,
    train_uid: &'a str,
    runs_from: &'a str,
    runs_to: &'a str,
    days_run: &'a str,
    bank_holidays_running: &'a str,
    train_status: &'a str,
    train_category: &'a str,
    train_identity: &'a str,
    headcode: &'a str,
    course_indicator: &'a str,
    code: &'a str,
    business_sector: &'a str,
    power_type: &'a str,
    timing_load: &'a str,
    speed: &'a str,
    operating_chars: &'a str,
    train_class: &'a str,
    sleepers: &'a str,
    reservations: &'a str,
    connect_indicator: &'a str,
    catering_code: &'a str,
    service_branding: &'a str,
    spare: &'a str,
    //Read in association with the Transaction Type in Field 2
    stp_indicator: STPIndicator,
}

fn parse_basic_schedule_record(input: &str) -> IResult<&str, BasicScheduleRecord> {
    let (input, _) = tag("BS")(input)?;
    let (input, transaction_type) = one_of("NDR").map(|s| TransactionType::from(s).unwrap()).parse(input)?;
    let (input, train_uid) = take_trim(6)(input)?;
    let (input, runs_from) = take_trim(6)(input)?;
    let (input, runs_to) = take_trim(6)(input)?;
    let (input, days_run) = take_trim(7)(input)?;
    let (input, bank_holidays_running) = take_trim(1)(input)?;
    let (input, train_status) = take_trim(1)(input)?;
    let (input, train_category) = take_trim(2)(input)?;
    let (input, train_identity) = take_trim(4)(input)?;
    let (input, headcode) = take_trim(4)(input)?;
    let (input, course_indicator) = take_trim(1)(input)?;
    let (input, code) = take_trim(8)(input)?;
    let (input, business_sector) = take_trim(1)(input)?;
    let (input, power_type) = take_trim(3)(input)?;
    let (input, timing_load) = take_trim(4)(input)?;
    let (input, speed) = take_trim(3)(input)?;
    let (input, operating_chars) = take_trim(6)(input)?;
    let (input, train_class) = take_trim(1)(input)?;
    let (input, sleepers) = take_trim(1)(input)?;
    let (input, reservations) = take_trim(1)(input)?;
    let (input, connect_indicator) = take_trim(1)(input)?;
    let (input, catering_code) = take_trim(4)(input)?;
    let (input, service_branding) = take_trim(4)(input)?;
    let (input, spare) = take_trim(1)(input)?;
    let (input, stp_indicator) = one_of("CNOP").map(|s| STPIndicator::from(s).unwrap()).parse(input)?;

    Ok((
        input,
        BasicScheduleRecord {
            // BSNC002062305212312030000001 POO2A205320122323000 DMUS   075      S            P
            transaction_type,
            train_uid,
            runs_from,
            runs_to,
            days_run,
            bank_holidays_running,
            train_status,
            train_category,
            train_identity,
            headcode,
            course_indicator,
            code,
            business_sector,
            power_type,
            timing_load,
            speed,
            operating_chars,
            train_class,
            sleepers,
            reservations,
            connect_indicator,
            catering_code,
            service_branding,
            spare,
            stp_indicator
        },
    ))
}

#[derive(Debug)]
pub struct BasicScheduleExtraDetails<'a> {
    uic_code: &'a str,
    atoc_code: &'a str,
    applicable_timetable_code: &'a str,
    retail_service_id: &'a str,
}

pub fn parse_basic_schedula_extra_detail(input: &str) -> IResult<&str, BasicScheduleExtraDetails> {
    let (input, _) = tag("BX")(input)?;
    let (input, _) = take_trim(4)(input)?;
    let (input, uic_code) = take_trim(5)(input)?;
    let (input, atoc_code) = take_trim(2)(input)?;
    let (input, applicable_timetable_code) = take_trim(1)(input)?;
    let (input, retail_service_id) = take_trim(8)(input)?;
    let (input, _) = take_trim(1)(input)?;
    let (input, _) = take_trim(57)(input)?;

    Ok((
        input,
        BasicScheduleExtraDetails {
            // BX         EMYEM532000                                                          
            uic_code,
            atoc_code,
            applicable_timetable_code,
            retail_service_id,
        },
    ))
}



#[derive(Debug)]
pub struct OriginLocation<'a> {
    location: &'a str,
    scheduled_departure_time: &'a str,
    public_departure_time: &'a str,
    platform: &'a str,
    line: &'a str,
    engineering_allowance: &'a str,
    pathing_allowance: &'a str,
    activity: &'a str,
    performance_allowance: &'a str,
}

pub fn parse_origin_location(input: &str) -> IResult<&str, OriginLocation> {
    let (input, _) = tag("LO")(input)?;
    let (input, location) = take_trim(8)(input)?;
    let (input, scheduled_departure_time) = take_trim(5)(input)?;
    let (input, public_departure_time) = take_trim(4)(input)?;
    let (input, platform) = take_trim(3)(input)?;
    let (input, line) = take_trim(3)(input)?;
    let (input, engineering_allowance) = take_trim(2)(input)?;
    let (input, pathing_allowance) = take_trim(2)(input)?;
    let (input, activity) = take_trim(12)(input)?;
    let (input, performance_allowance) = take_trim(2)(input)?;
    let (input, _) = take_trim(37)(input)?;

    Ok((
        input, OriginLocation {
            // LODRBY    0756 07565B C      TB                                                 
            location,
            scheduled_departure_time,
            public_departure_time,
            platform,
            line,
            engineering_allowance,
            pathing_allowance,
            activity,
            performance_allowance,
        },
    ))
}


#[derive(Debug)]
pub struct IntermediateLocation<'a> {
    location: &'a str,
    scheduled_arrival_time: &'a str,
    scheduled_departure_time: &'a str,
    scheduled_pass: &'a str,
    public_arrival_time: &'a str,
    public_departure_time: &'a str,
    platform: &'a str,
    line: &'a str,
    path: &'a str,
    activity: &'a str,
    engineering_allowance: &'a str,
    pathing_allowance: &'a str,
    performance_allowance: &'a str,
}

pub fn parse_intermediate_location(input: &str) -> IResult<&str, IntermediateLocation> {
    let (input, _) = tag("LI")(input)?;
    let (input, location) = take_trim(8)(input)?;
    let (input, scheduled_arrival_time) = take_trim(5)(input)?;
    let (input, scheduled_departure_time) = take_trim(5)(input)?;
    let (input, scheduled_pass) = take_trim(5)(input)?;
    let (input, public_arrival_time) = take_trim(4)(input)?;
    let (input, public_departure_time) = take_trim(4)(input)?;
    let (input, platform) = take_trim(3)(input)?;
    let (input, line) = take_trim(3)(input)?;
    let (input, path) = take_trim(3)(input)?;
    let (input, activity) = take_trim(12)(input)?;
    let (input, engineering_allowance) = take_trim(2)(input)?;
    let (input, pathing_allowance) = take_trim(2)(input)?;
    let (input, performance_allowance) = take_trim(2)(input)?;
    let (input, _) = take_trim(20)(input)?;

    Ok((
        input, IntermediateLocation {
            // LODRBY    0756 07565B C      TB                                                 
            location,
            scheduled_arrival_time,
            scheduled_departure_time,
            scheduled_pass,
            public_arrival_time,
            public_departure_time,
            platform,
            line,
            path,
            engineering_allowance,
            pathing_allowance,
            activity,
            performance_allowance,
        },
    ))
}


#[derive(Debug)]
pub struct ChangedRoute<'a> {
    any: &'a str
}

pub fn parse_changed_route(input: &str) -> IResult<&str, ChangedRoute> {
    let (input, _) = tag("CR")(input)?;
    let (input, any) = take_trim(78)(input)?;

    Ok((
        input, ChangedRoute {
            // LODRBY    0756 07565B C      TB                                                 
            any
        },
    ))
}



#[derive(Debug)]
pub struct TerminatingLocation<'a> {
    location: &'a str,
    scheduled_arrival_time: &'a str,
    public_arrival_time: &'a str,
    platform: &'a str,
    path: &'a str,
    activity: &'a str,
}

pub fn parse_terminating_location(input: &str) -> IResult<&str, TerminatingLocation> {
    let (input, _) = tag("LT")(input)?;
    let (input, location) = take_trim(8)(input)?;
    let (input, scheduled_arrival_time) = take_trim(5)(input)?;
    let (input, public_arrival_time) = take_trim(4)(input)?;
    let (input, platform) = take_trim(3)(input)?;
    let (input, path) = take_trim(3)(input)?;
    let (input, activity) = take_trim(12)(input)?;
    let (input, _) = take_trim(43)(input)?;

    Ok((
        input, TerminatingLocation {
            // LODRBY    0756 07565B C      TB                                                 
            location,
            scheduled_arrival_time,
            public_arrival_time,
            platform,
            path,
            activity,
        },
    ))
}



#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn parse_example_basic_schedule_record() {
        let input = "BSNC002062305212312030000001 POO2A205320122323000 DMUS   075      S            P";

        match all_consuming(parse_basic_schedule_record).parse(input) {
            Ok((_, res)) => {
                println!("{:?}", res);
            }
            Err(e) => panic!("Error: {:?}", e),
        }
    }

    #[test]
    fn parse_example_basic_schedule_extra_detail() {
        let input = "BX         EMYEM532000                                                          ";

        match all_consuming(parse_basic_schedula_extra_detail).parse(input) {
            Ok((_, res)) => {
                println!("{:?}", res);
            }
            Err(e) => panic!("Error: {:?}", e),
        }
    }


    #[test]
    fn parse_example_origin_location() {
        let input = "LODRBY    0756 07565B C      TB                                                 ";

        match all_consuming(parse_origin_location).parse(input) {
            Ok((_, res)) => {
                println!("{:?}", res);
            }
            Err(e) => panic!("Error: {:?}", e),
        }
    }

    #[test]
    fn parse_example_intermediate_location() {
        let input = "LIDRBYSMS           0758 00000000   DF                                          ";

        match all_consuming(parse_intermediate_location).parse(input) {
            Ok((_, res)) => {
                println!("{:?}", res);
            }
            Err(e) => panic!("Error: {:?}", e),
        }
    }

    #[test]
    fn parse_example_changed_route() {
        let input = "CRDRBY    OO2A225322122323000 DMUS   075      S                    EM532200     ";

        match all_consuming(parse_changed_route).parse(input) {
            Ok((_, res)) => {
                println!("{:?}", res);
            }
            Err(e) => panic!("Error: {:?}", e),
        }
    }

    #[test]
    fn parse_example_terminating_location() {
        let input = "LTMATLOCK 1031 10311     TF                                                     ";

        match all_consuming(parse_terminating_location).parse(input) {
            Ok((_, res)) => {
                println!("{:?}", res);
            }
            Err(e) => panic!("Error: {:?}", e),
        }
    }
}
