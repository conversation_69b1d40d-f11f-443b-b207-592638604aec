use crate::feed_parsing::parse_comment_block;
use nom::{
    character::complete::{char, line_ending, multispace0, none_of, one_of},
    combinator::{map, recognize, opt, value, all_consuming},
    multi::{many1, many_m_n, many0},
    sequence::{pair, terminated, tuple, preceded, delimited},
    IResult, branch::alt, Parser,
    bytes::complete::take, error::{context, convert_error},
};
use crate::feed_parsing::parse_comment;

#[derive(Debug)]
pub struct TOCRecord<'a> {
    toc_id: &'a str,
    toc_name: &'a str,
    active_indicator: bool,
}

#[derive(Debug)]
pub struct FareTOCRecord<'a> {
    fare_toc_id: &'a str,
    toc_id: Option<&'a str>,
    fare_toc_name: &'a str,
}

fn parse_toc_record(input: &str) -> IResult<&str, TOCRecord> {
    let (input, (_, toc_id, toc_name, _, active_indicator, _)) = tuple((
        char('T'),
        take(2u8),
        take(30u8),
        take(8u8),
        alt((value(true, char('Y')), value(false, char('N')))), 
        line_ending,
    ))(input)?;

    Ok((
        input,
        TOCRecord {
            toc_id: toc_id,
            toc_name: toc_name.trim_end(),
            active_indicator,
        },
    ))
}

fn parse_fare_toc_record(input: &str) -> IResult<&str, FareTOCRecord> {
    let (input, (_, fare_toc_id, toc_id, fare_toc_name, _)) = tuple((
        char('F'),
        take(3 as usize),
        take(2 as usize).map(|s: &str| (!s.trim().is_empty()).then_some(s)),
        take(30 as usize),
        line_ending,
    ))(input)?;

    Ok((
        input,
        FareTOCRecord {
            fare_toc_id,
            toc_id,
            fare_toc_name: fare_toc_name.trim_end(),
        },
    ))
}

fn parse_content(input: &str) -> IResult<&str, (Vec<FareTOCRecord>, Vec<TOCRecord>)> {
    tuple((many1(parse_fare_toc_record), many1(parse_toc_record)))(input)
}

pub fn parse_toc(input: &str) -> IResult<&str, (Vec<FareTOCRecord>, Vec<TOCRecord>)> {
    delimited(parse_comment_block, parse_content, parse_comment_block)(input)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn parse_example_toc() {
        let input = "/!! Start of file                                                               
/!! Content type:  TOC                                                          
/!! Sequence:      648                                                          
/!! Records:       143                                                          
/!! Generated:     24/04/2023                                                   
/!! Exporter:      RJIS RjEhrFrs v 4.6.5                                        
FSWTSWSOUTH WESTERN RAILWAY         
FTAC  WATFORD TAAC FOR CSV FILE ONLY
FTGNGNTHAMESLINK AND GT NORTHERN GN 
FTLK  THAMESLINK                    
FTPETPTRANSPENNINE EXPRESS          
FWAB  WALES & BORDERS               
FWGN  WAGN                          
FWSM  WREXHAM SHROPSHIRE MARYLEBONE 
FWSX  WESSEX TRAINS                 
TAMARRIVA MERSEYRAIL                     N
TANARRIVA NORTHERNSPIRIT                 N
TARANGLIA RAILWAYS TRAIN SERVICES        N
TAWTRANSPORT FOR WALES RAIL              Y
TCCC2C                                   Y
TCHCHILTERN RAILWAYS                     Y
TCSSERCO CALEDONIAN SLEEPER              Y
/!! End of file (24/04/2023)
";
        
        
        match all_consuming(parse_toc).parse(input) {
            Ok((_, (toc_records, fare_records))) => {
                println!("Fare Records:");
                for record in toc_records {
                    println!("{:?}", record);
                }
                println!("\nTOC Records:");
                for record in fare_records {
                    println!("{:?}", record);
                }
            }
            Err(e) => panic!("Error: {:?}", e),
        }
    }
}
