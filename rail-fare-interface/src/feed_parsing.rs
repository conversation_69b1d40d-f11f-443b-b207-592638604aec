use nom::{
    character::complete::{char, line_ending, multispace0, none_of, one_of},
    combinator::{map, recognize, opt, value, all_consuming},
    multi::{many1, many_m_n, many0},
    sequence::{pair, terminated, tuple, preceded},
    IResult, branch::alt, Parser,
    bytes::complete::{take, take_until}, error::{context, convert_error, ParseError},
};

#[derive(Debug)]
struct TOCRecord<'a> {
    toc_id: &'a str,
    toc_name: &'a str,
    active_indicator: bool,
}

#[derive(Debug)]
struct FareTOCRecord<'a> {
    fare_toc_id: &'a str,
    toc_id: Option<&'a str>,
    fare_toc_name: &'a str,
}

pub fn parse_comment(input: &str) -> IResult<&str, &str> {
    let (input, _) = char('/')(input)?;
    let (input, comment) = terminated(take_until("\n"), char('\n'))(input)?;
    Ok((
        input,
        comment
    ))
}

pub fn parse_comment_block(input: &str) -> IResult<&str, Vec<&str>> {
    many0(parse_comment)(input)
}

pub fn take_trim<'i>(count: u8) -> impl FnMut(&'i str) -> IResult<&'i str, &'i str, nom::error::Error<&'i str>>
{
    move |input: &str| {
        take(count).map(|s: &'i str| s.trim()).parse(input)
    }
}
