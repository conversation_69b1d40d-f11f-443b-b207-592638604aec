

## Custom parser combinator

Failed to write the type for this when I tried:

https://stackoverflow.com/questions/67722023/how-to-use-rust-nom-to-write-a-parser-for-this-kind-of-structure-text

```
pub fn body_line<'i>(title: &'i str) -> impl FnMut(&'i str) -> IResult<&'i str, &'i str, nom::error::Error<&'i str>>
{
    move |input: &str| {
        delimited(
            pair(tag(title), tag(" ")),
            take_while(not_r_n),
            end_of_line,
        )(input)
    }
}
```

## Timetable routes: Connection Scan Algorithm

https://github.com/trainline-eu/csa-challenge includes pseudocode and actual code of the alg

an impl in rust https://github.com/Tristramg/csa-rust/tree/master

way more algs with good explanation https://www.youtube.com/watch?v=AdArDN4E6Hg