name: "Update resources usage plots 📈 and create PR"
on: workflow_dispatch

env:
  PYTHON_VERSION: 3.12

jobs:
  bump-n-pr:
    name: Update resources usage plots 📈 and create PR
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.CICD_PAT_TOKEN }}
      - name: Configure Git user
        run: |
          git config --local user.name "Kraina CI/CD"
          git config --local user.email "<EMAIL>"
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-dev-${{ hashFiles('**/pdm.lock') }}
          restore-keys: |
            ${{ runner.os }}-pip-dev-
      - name: Install pdm
        run: pip install pdm
      - name: Generate requirements.txt
        run: pdm export --no-default -G docs -G visualization -G cli-dev -f requirements -o requirements.txt
      - name: Install dependencies
        run: pip install --no-deps -r requirements.txt
      - name: Install quackosm
        run: |
          pdm build -v -d dist
          pip install 'quackosm[cli] @ file://'"$(pwd)/$(find dist -name '*.whl')" --user
      - name: Execute jupyter notebooks
        run: jupyter nbconvert --to notebook --inplace --execute $(find dev/ -type f -name "*.ipynb") --ExecutePreprocessor.kernel_name='python3'
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          title: "chore: update resources usage plots"
          branch: update-resources-usage-plots
          base: main
          labels: Skip-Changelog
          add-paths: docs/assets/images/*.png
          token: ${{ secrets.CICD_PAT_TOKEN }}
          author: Kraina CI/CD <<EMAIL>>
          committer: Kraina CI/CD <<EMAIL>>
          commit-message: "chore: update resources usage plots"
