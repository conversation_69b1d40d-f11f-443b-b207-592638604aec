name: "Run manual pre-commit stage"
on:
  pull_request:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  run-manual-stage:
    name: Run pre-commit manual stage
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.12"
      - uses: pre-commit/action@v3.0.0
        with:
          extra_args: --all-files --hook-stage manual --verbose
