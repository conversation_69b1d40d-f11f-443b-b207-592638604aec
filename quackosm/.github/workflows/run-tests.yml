name: "Run tests workflow"
on:
  pull_request:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  run-tests:
    name: "Run tests job"
    uses: ./.github/workflows/_tests.yml
    secrets: inherit

  run-tests-newest-dependencies:
    if: contains(github.event.pull_request.labels.*.name, 'release')
    name: "Run tests with newest dependencies job"
    uses: ./.github/workflows/manual_tests.yml
    secrets: inherit
