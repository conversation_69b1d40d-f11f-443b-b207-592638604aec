name: "Run tests with newest and oldest dependencies"
on: [workflow_dispatch, workflow_call]

jobs:
  run-tests-newest:
    name: Run tests 🛠️ on multiple systems 🖥️ and Python 🐍 versions (newest dependencies)
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-13, macos-latest, windows-latest]
        python-version: ["3.12"]
    env:
      OS: ${{ matrix.os }}
      PYTHON: ${{ matrix.python-version }}
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install pdm
        run: pip install pdm
      - name: Generate lock with newest dependencies
        run: pdm lock --lockfile pdm.newest.lock --strategy no_cross_platform -dG:all
      - name: Install quackosm and tests dependencies
        run: pdm install --lockfile pdm.newest.lock -dG:all --skip=post_install
      - name: Run tests with pytest
        run: |
          pdm run pytest -v -s --durations=20 --doctest-modules --doctest-continue-on-failure quackosm
          pdm run pytest -v -s --durations=20 tests/base
          pdm run pytest -v -s --durations=20 tests/optional_imports

  run-tests-oldest:
    name: Run tests 🛠️ on multiple systems 🖥️ and Python 🐍 versions (oldest dependencies)
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-13, macos-latest, windows-latest]
        python-version: ["3.9"]
    env:
      OS: ${{ matrix.os }}
      PYTHON: ${{ matrix.python-version }}
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install pdm
        run: pip install pdm
      - name: Generate lock with oldest dependencies
        run: pdm lock --lockfile pdm.oldest.lock --strategy no_cross_platform,direct_minimal_versions -dG:all
      - name: Install srai and tests dependencies
        run: pdm install --lockfile pdm.oldest.lock -dG:all --skip=post_install
      - name: Run tests with pytest
        run: |
          pdm run pytest -v -s --durations=20 tests/base
          pdm run pytest -v -s --durations=20 tests/optional_imports
