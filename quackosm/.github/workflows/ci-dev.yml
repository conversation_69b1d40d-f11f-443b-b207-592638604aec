name: "Build & publish - DEV"
on:
  workflow_run:
    workflows: [Test - DEV]
    types:
      - completed

jobs:
  build-n-publish:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    name: Build and publish Python 🐍 distributions 📦 to TestPyPI
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: 3.12
      - uses: pdm-project/setup-pdm@v3
        name: Setup PDM
        with:
          python-version: 3.12
          architecture: x64
          enable-pep582: true
          cache: true
          cache-dependency-path: "**/pdm.lock"
      - run: pip install toml
      - uses: jannekem/run-python-script-action@v1
        name: Rename test version
        with:
          script: |
            import time
            import toml
            data = toml.load("pyproject.toml")
            current_date = time.strftime("%Y%m%d%H%M%S")
            data['project']['version']=f"{data['project']['version']}-{current_date}"
            data['tool']['bumpver']['current_version']=f"{data['tool']['bumpver']['current_version']}-{current_date}"
            f = open("pyproject.toml",'w')
            toml.dump(data, f)
            f.close()
      - name: Publish distribution 📦 to Test PyPI
        run: pdm publish --repository testpypi --username __token__ --password ${{ secrets.PYPI_TEST_API_TOKEN }} --comment ${{ github.sha }} --verbose

  generate-docs:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    name: Generate latest library 📚 documentation 📄
    runs-on: ubuntu-latest
    permissions:
      contents: write
    env:
      MKDOCS_EXECUTE_JUPYTER: false # execution is done before rendering documentation
      MKDOCS_DEV: true
      FORCE_TERMINAL_MODE: true
    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.CICD_PAT_TOKEN }}
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.12"
      - uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-dev-${{ hashFiles('**/pdm.lock') }}
          restore-keys: |
            ${{ runner.os }}-pip-dev-
      - name: Install pdm
        run: pip install pdm
      - name: Generate requirements.txt
        run: pdm export --no-default -G docs -G visualization -G cli-dev -f requirements -o requirements.txt
      - name: Install dependencies
        run: pip install --no-deps -r requirements.txt
      - name: Install quackosm
        run: |
          pdm build -v -d dist
          pip install 'quackosm[cli] @ file://'"$(pwd)/$(find dist -name '*.whl')" --user
      - name: Configure Git user
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
      - name: Cache OSM data
        uses: actions/cache@v3
        with:
          path: "**/cache"
          key: mkdocs-osm-dev-cache-${{ runner.os }}
      - name: Prepare DuckDB dependency
        run: |
          wget https://github.com/duckdb/duckdb/releases/download/v0.10.2/duckdb_cli-linux-amd64.zip
          unzip duckdb_cli-linux-amd64.zip
          chmod a+x ./duckdb
      - name: Test required commands
        run: |
          ./duckdb :memory: "SELECT 1"
          QuackOSM -h
      - name: Execute jupyter notebooks
        run: |
          mv ./duckdb ./examples/duckdb
          jupyter nbconvert --to notebook --inplace --execute $(find examples/ -type f -name "*.ipynb") --ExecutePreprocessor.kernel_name='python3'
          rm ./examples/duckdb
      - uses: jannekem/run-python-script-action@v1
        name: Replace copyright date
        with:
          script: |
            import time
            file_name = "mkdocs.yml"
            with open(file_name) as f:
                data = f.read().replace('{current_year}', time.strftime("%Y"))
            with open(file_name, "w") as f:
                f.write(data)
      - name: Create remote for quackosm-docs repository
        run: git remote add origin-quackosm-docs https://github.com/kraina-ai/quackosm-docs
      - name: Fetch gh-pages branch
        run: git fetch origin-quackosm-docs gh-pages --depth=1
      - name: Publish dev documentation
        run: mike deploy --remote origin-quackosm-docs --rebase --push dev

  deploy-docs:
    name: Deploy documentation 📄 to Github Pages 🌐
    needs: [generate-docs]
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pages: write
      id-token: write
    steps:
      - uses: actions/checkout@v3
        with:
          repository: 'kraina-ai/quackosm-docs'
          ref: 'gh-pages'
          token: ${{ secrets.CICD_PAT_TOKEN }}
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v2
        with:
          # Upload entire repository
          path: '.'
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v2
