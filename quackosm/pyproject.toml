[project]
name = "QuackOSM"
version = "0.8.2"
description = "An open-source tool for reading OpenStreetMap PBF files using DuckDB"
authors = [{ name = "<PERSON><PERSON><PERSON>", email = "k<PERSON><PERSON><PERSON>@kraina.ai" }]
dependencies = [
    "geopandas>=0.6",
    "shapely>=2",
    "pyarrow>=16.0.0",
    "duckdb>=0.10.2",
    "geoarrow-pyarrow>=0.1.2",
    "geoarrow-pandas>=0.1.1",
    "typeguard>=3.0.0",
    "psutil>=5.6.2",
    "pooch>=1.6.0",
    "tqdm>=4.42.0",
    "beautifulsoup4",
    "pyarrow-ops",
    "requests",
    "polars>=0.19.4",
    "rich>=10.11.0",
    "geoarrow-rust-core>=0.2.0",
    "certifi",
]
requires-python = ">=3.9"
readme = "README.md"
license = { text = "Apache-2.0" }
classifiers = [
    "Development Status :: 3 - Alpha",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: GIS",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: Unix",
    "Operating System :: MacOS",
    "Operating System :: Microsoft :: Windows",
]

[project.urls]
Homepage = "https://kraina-ai.github.io/quackosm"
Repository = "https://github.com/kraina-ai/quackosm"
Documentation = "https://kraina-ai.github.io/quackosm"
Changelog = "https://github.com/kraina-ai/quackosm/blob/main/CHANGELOG.md"

[project.optional-dependencies]
cli = [
    "typer[all]>=0.9.0",
    "osmnx>=1.3.0",
    "h3>=4.0.0b1",
    "s2>=0.1.9",
    "python-geohash>=0.8",
]

[project.scripts]
quackosm = "quackosm.__main__:main"
QuackOSM = "quackosm.__main__:main"

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[tool]
[tool.pdm]
[tool.pdm.dev-dependencies]
# pdm add -d <library>
dev = [
    "bumpver",
    "types-requests",
    "setuptools>=45.0.0",
]
# pdm add -dG lint <library>
lint = ["pre-commit", "mypy>=1", "docformatter[tomli]", "ruff>=0.1.0"]
# pdm add -dG test <library>
test = [
    "pytest>=7.0.0",
    "tox-pdm",
    "pytest-mock>=3.3.0",
    "requests-mock",
    "pytest-check",
    "pytest-parametrization",
    "pytest-doctestplus",
    "srai>=0.6.2",
]
# pdm add -dG docs <library>
docs = [
    "mkdocs",
    "mkdocs-material",
    "mkdocs-mermaid2-plugin",
    "mkdocstrings[python]",
    "mkdocs-jupyter",
    "ipykernel",
    "mkdocs-gen-files",
    "mkdocs-awesome-pages-plugin",
    "mike>=1,<2",
    "black",
    "jupyter",
    "nbconvert",
    "nbformat",
    "notebook",
    "osmnx",
    "matplotlib",
    "seaborn>=0.10.0",
    "py-cpuinfo",
    "adjustText",
]
license = ["licensecheck"]
cli-dev = [
    "ipywidgets",
    "folium",
    "matplotlib",
    "mapclassify",
]

[tool.pdm.scripts]
post_install = "pre-commit install"

[tool.black]
line-length = 100
target-version = ["py39", "py310", "py311", "py312"]
preview = true

[tool.ruff]
line-length = 100
target-version = "py39"
extend-exclude = ["old"]

[tool.ruff.lint]
select = [
    "E",
    "W",   # pycodestyle
    "F",   # pyflakes
    "UP",  # pyupgrade
    "D",   # pydocstyle
    "I",   # isort
    "B",   # flake8-bugbear
    "NPY", # NumPy
    "YTT", # flake8-2020
    "Q",   # flake8-quotes
    "PLE",
    "PLW", # pylint (add "PLR" in the future)
    "PIE", # misc lints
    "TID", # tidy imports
    "ISC", # implicit string concatenation
    "TCH", # type-checking imports
    # "N",            # pep8-naming
    # "ANN",          # flake8-annotations
]
ignore = ["D212"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.pycodestyle]
max-doc-length = 100

[tool.mypy]
strict = true
show_column_numbers = true
disallow_untyped_defs = true
no_implicit_optional = true
check_untyped_defs = true
warn_return_any = true

[tool.docformatter]
syntax = 'google'
black = true
recursive = true
wrap-summaries = 100
wrap-descriptions = 100
# force-wrap = false # uncomment after https://github.com/PyCQA/docformatter/issues/68 is resolved
tab-width = 4
blank = false
pre-summary-newline = true
close-quotes-on-newline = true
wrap-one-line = true

[tool.bumpver]
current_version = "0.8.2"
version_pattern = "MAJOR.MINOR.PATCH[PYTAGNUM]"
commit_message = "chore(CI/CD): bump version {old_version} -> {new_version}"
commit = true
tag = false
push = false

[tool.bumpver.file_patterns]
"pyproject.toml" = [
    '^current_version = "{version}"$',
    '^version = "{version}"$',
]
"quackosm/__init__.py" = ['^__version__ = "{version}"$']

[tool.pytest.ini_options]
addopts = ["--import-mode=importlib"]
markers = ["slow: marks tests as slow (deselect with '-m \"not slow\"')"]
log_cli = true
doctest_optionflags = ['ELLIPSIS', 'NORMALIZE_WHITESPACE', 'IGNORE_EXCEPTION_DETAIL']

[tool.licensecheck]
using = "requirements"
zero = false
ignore_licenses = ["UNKNOWN"]
ignore_packages = [
    'docformatter',             # uses MIT license, has mismatched license in analysis
    'mkdocs-jupyter',           # uses Apache-2.0 license, has mismatched license in analysis
]
