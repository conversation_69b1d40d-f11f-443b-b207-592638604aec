[tox]
envlist =
    python3.9
    python3.10
    python3.11
    python3.12
isolated_build = True
skip_missing_interpreters = True

[testenv]
groups =
    test
    cli
deps =
    coverage
    pre-commit
commands =
    coverage run --data-file=.coverage.doc.tests --source=quackosm -m pytest -v -s --durations=20 --doctest-modules --doctest-continue-on-failure quackosm
    coverage run --data-file=.coverage.base.tests --source=quackosm -m pytest -v -s --durations=20 tests/base
    coverage run --data-file=.coverage.optional.tests --source=quackosm -m pytest -v -s --durations=20 tests/optional_imports
    coverage run --data-file=.coverage.benchmark.tests --source=quackosm -m pytest -v -s --durations=20 tests/benchmark
    coverage combine
    coverage xml -o coverage.{envname}.xml
    coverage report -m
skip_install = true
