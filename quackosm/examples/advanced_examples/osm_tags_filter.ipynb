{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OSM tags filter\n", "\n", "**QuackOSM** allows users to filter the data from the `*.osm.pbf` file. Filtering will reduce a number of features parsed from the original file.\n", "\n", "This notebook will explain how to use the OSM tags filtering mechanism."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filter format\n", "\n", "Library expects a filter in the `dict` form (or `JSON` if provided via CLI).\n", "\n", "**QuackOSM** uses two formats of filters: `OsmTagsFilter` and `GroupedOsmTagsFilter`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from quackosm._osm_tags_filters import GroupedOsmTagsFilter, OsmTagsFilter"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The first one, `OsmTagsFilter`, is a basic `dict` object that defines how to filter OSM based on their tags.\n", "\n", "It is based on the filter object used in the [OSMnx](https://osmnx.readthedocs.io/en/stable/index.html) library, but it has more functionalities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OsmTagsFilter"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The key of the `dict` is expected to be an OSM tag key and the value can be one of: `bool`, a single OSM tag value or a list of OSM tag values."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# amenity=bench\n", "filter_1 = {\"amenity\": \"bench\"}\n", "\n", "# amenity=ice_cream and amenity=cafe\n", "filter_2 = {\"amenity\": [\"ice_cream\", \"cafe\"]}\n", "\n", "# all amenities\n", "filter_3 = {\"amenity\": True}\n", "\n", "# amenity=bar and building=office\n", "filter_4 = {\"amenity\": \"bar\", \"building\": \"office\"}\n", "\n", "# all amenities and all highways\n", "filter_5 = {\"amenity\": True, \"highway\": True}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Second object, `GroupedOsmTagsFilter`, allows assigning filters to groups. It is a `dict` object with a group name being a `key` and `OsmTagsFilter` being a value.\n", "\n", "This can become useful for grouping features into semantical categories for machine learning applications."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GroupedOsmTagsFilter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# benches\n", "grouped_filter_1 = {\"benches\": {\"amenity\": \"bench\"}}\n", "\n", "# swimming sport facilities\n", "grouped_filter_2 = {\"swimming_sport\": {\"leisure\": \"swimming_pool\", \"sport\": \"swimming\"}}\n", "\n", "# shops, tourism and traffic related objects\n", "grouped_filter_3 = {\n", "    \"shopping\": {\"shop\": True, \"landuse\": \"retail\"},\n", "    \"tourism\": {\"tourism\": True, \"historic\": True},\n", "    \"traffic\": {\"amenity\": \"parking\", \"highway\": True},\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic usage\n", "\n", "Examples below show how to use the basic OSM tags filters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import urllib.request\n", "\n", "from quackosm import convert_pbf_to_geodataframe"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["monaco_pbf_url = \"https://download.geofabrik.de/europe/monaco-latest.osm.pbf\"\n", "monaco_pbf_file = \"monaco.osm.pbf\"\n", "urllib.request.urlretrieve(monaco_pbf_url, monaco_pbf_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Benches only"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"amenity\": \"bench\"}\n", "convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cafes, bars and restaurants"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"amenity\": [\"cafe\", \"restaurant\", \"bar\"]}\n", "convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### All amenities and leisures"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"amenity\": True, \"leisure\": True}\n", "convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Shopping and tourism related objects (grouped filters)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grouped_tags_filter = {\n", "    \"shopping\": {\"shop\": True, \"landuse\": \"retail\"},\n", "    \"tourism\": {\"tourism\": True, \"historic\": True},\n", "}\n", "convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=grouped_tags_filter, verbosity_mode=\"silent\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compact and exploded tags\n", "\n", "**QuackOSM** has the option to operate in two distinct modes when it comes to the result `GeoParquet` file schema:\n", "- The first one keeps all loaded OSM tags as a `dict` object under the `tag` column name. This format can simplify storage schema (just 3 columns) and is recommended for a big number of loaded osm tags (or when there is no `tags_filter` applied).\n", "- The second one generates one column per each OSM tag key (or per group name when using `GroupedOsmTagsFilter`). This mode can simplify further operations in the geospatial analyses.\n", "\n", "Section below explains the difference between them and how they interact with other available parameters."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Parameters logic table.**\n", "\n", "The table shows how the columns for the result are generated based on value of the `explode_tags`, `keep_all_tags` parameters with and without OSM tags filter being present.\n", "\n", "Legend:\n", "\n", "- ✔️ - `True`\n", "- ❌ - `False`\n", "- 📦 - Compact tags (single `tags` column)\n", "- 💥 - Exploded tags (separate columns per each tag key, or group name)\n", "\n", "<style type=\"text/css\">\n", ".tg  {border-collapse:collapse;border-spacing:0;margin:0px auto;}\n", ".tg td{border-style:solid;border-width:1px;font-size:1em;\n", "  overflow:hidden;padding:10px 5px;word-break:normal;}\n", ".tg th{border-style:solid;border-width:1px;font-size:1em;\n", "  font-weight:normal;overflow:hidden;padding:10px 5px;word-break:normal;}\n", ".tg .tg-0so2{font-family:inherit;text-align:center;vertical-align:middle}\n", ".tg .tg-header{background-color:var(--jp-rendermime-table-row-background);}\n", ".tg .tg-highlight{background-color:var(--jp-rendermime-table-row-hover-background);}\n", "@media screen and (max-width: 767px) {.tg {width: auto !important;}.tg col {width: auto !important;}.tg-wrap {overflow-x: auto;-webkit-overflow-scrolling: touch;margin: auto 0px;}}</style>\n", "<div class=\"tg-wrap\"><table class=\"tg\">\n", "<thead>\n", "  <tr class=\"tg-header\">\n", "    <th class=\"tg-0so2\"><strong>OSM filter</strong><sup>1</sup></th>\n", "    <th class=\"tg-0so2\"><strong><code>keep_all_tags</code></strong><sup>2</sup></th>\n", "    <th class=\"tg-0so2\"><strong><code>explode_tags</code></strong></th>\n", "    <th class=\"tg-0so2\"><strong>Resulting columns</strong></th>\n", "  </tr>\n", "</thead>\n", "<tbody>\n", "  <tr>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\"><code>None</code></td>\n", "    <td class=\"tg-0so2\">📦</td>\n", "  </tr>\n", "  <tr>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">💥</td>\n", "  </tr>\n", "  <tr>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\">📦</td>\n", "  </tr>\n", "  <tr class=\"tg-highlight\">\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\"><code>None</code></td>\n", "    <td class=\"tg-0so2\">💥<sup>3</sup></td>\n", "  </tr>\n", "  <tr>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">💥</td>\n", "  </tr>\n", "  <tr>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\">📦</td>\n", "  </tr>\n", "  <tr class=\"tg-highlight\">\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\"><code>N/A</code></td>\n", "    <td class=\"tg-0so2\"><code>None</code></td>\n", "    <td class=\"tg-0so2\">📦<sup>4</sup></td>\n", "  </tr>\n", "  <tr>\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\"><code>N/A</code></td>\n", "    <td class=\"tg-0so2\">✔️</td>\n", "    <td class=\"tg-0so2\">💥</td>\n", "  </tr>\n", "  <tr>\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\"><code>N/A</code></td>\n", "    <td class=\"tg-0so2\">❌</td>\n", "    <td class=\"tg-0so2\">📦</td>\n", "  </tr>\n", "</tbody>\n", "</table></div><br/>\n", "<div style=\"font-size:0.8em;\">\n", "<code>1</code> - refers to the `<code>tags_filter</code> parameter: ✔️ - filter is provided, ❌ - filter is empty<br/>\n", "<code>2</code> - <code>keep_all_tags</code> is used only when <code>tags_filter</code> is provided (<code>N/A</code> - not applicable) <br/>\n", "<code>3</code> - default parameter values with <code>tags_filter</code> <br/>\n", "<code>4</code> - default parameter values without <code>tags_filter</code>\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Examples below show how to use `explode_tags` and `keep_all_tags` parameters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"amenity\": \"library\"}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Default mode with OSM tags filter\n", "\n", "Will load only OSM tags from the provided filter. <br/>\n", "Default parameter values: `explode_tags`=✔️, `keep_all_tags`=❌"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Default mode without OSM tags filter\n", "\n", "Load all features from the PBF file. <br/>\n", "Default parameter values: `explode_tags`=❌, `keep_all_tags`=`N/A` (takes all OSM tags, since there is no filter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=None, verbosity_mode=\"silent\").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. `explode_tags`=✔️, `keep_all_tags`=❌\n", "\n", "Keep only tags from the provided `tags_filter` and save them as separate columns."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(\n", "    monaco_pbf_file,\n", "    tags_filter=tags_filter,\n", "    explode_tags=True,\n", "    keep_all_tags=False,\n", "    verbosity_mode=\"silent\",\n", ").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. `explode_tags`=✔️, `keep_all_tags`=✔️\n", "\n", "Keep all OSM tags and save them as separate columns."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(\n", "    monaco_pbf_file,\n", "    tags_filter=tags_filter,\n", "    explode_tags=True,\n", "    keep_all_tags=True,\n", "    verbosity_mode=\"silent\",\n", ").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. `explode_tags`=❌, `keep_all_tags`=❌\n", "\n", "Keep only tags from the provided `tags_filter` and save them as a single `tags` column."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(\n", "    monaco_pbf_file,\n", "    tags_filter=tags_filter,\n", "    explode_tags=False,\n", "    keep_all_tags=False,\n", "    verbosity_mode=\"silent\",\n", ").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. `explode_tags`=❌, `keep_all_tags`=✔️\n", "\n", "Keep all OSM tags and save them as a single `tags` column."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(\n", "    monaco_pbf_file,\n", "    tags_filter=tags_filter,\n", "    explode_tags=False,\n", "    keep_all_tags=True,\n", "    verbosity_mode=\"silent\",\n", ").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Positive and negative filters\n", "\n", "In the first part of this page it was mentioned that the filter format is inspired by the solution known from the OSMnx library, but it is extended with additional functionalities.\n", "\n", "The first of these is the ability to specify a `False` value to exclude objects that have a particular tag.\n", "\n", "To make it easier to explain the logic behind this solution, the primary filter values (`str`, `list[str]` and `True` value) will be called **positive** ➕ and the `False` value - **negative** ➖.\n", "\n", "There is no option to pass `str` or a `list[str]` as a negative filter with `tags_filter` mechanism.\n", "\n", "The following section will explain how these two interact and how the filtering logic works in the presence of both positive and negative filters."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### No buildings and highways\n", "\n", "This is the pure negative OSM tags filter, that will exclude all of the features that are either buildings or highways. Negative filter values are used only to filter out features, so all other tags are still in the result."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"building\": False, \"highway\": False}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Filter condition:\n", "```python\n", "\"building\" not in tags.keys and \"highway\" not in tags.keys\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fuel stations without brand name\n", "\n", "Here we will select the fuel stations but only those that don't have a `brand` tag. To make it easier to see all tags, we will force it to keep all of them."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"amenity\": \"fuel\", \"brand\": False}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Filter condition:\n", "\n", "```python\n", "tags[\"amenity\"] == \"fuel\" and \"brand\" not in tags.keys\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(\n", "    monaco_pbf_file,\n", "    tags_filter=tags_filter,\n", "    verbosity_mode=\"silent\",\n", "    keep_all_tags=True,\n", "    explode_tags=True,\n", ").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Shops and restaurants without defined opening hours and website"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"shop\": True, \"amenity\": \"restaurant\", \"opening_hours\": False, \"website\": False}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Filter condition:\n", "\n", "```python\n", "(\"shop\" in tags.keys or tags[\"amenity\"] == \"restaurant\")\n", "and (\"opening_hours\" not in tags.keys and \"website\" not in tags.keys)\n", "```\n", "\n", "Here you can clearly see that positive filters require just one condition to be satisfied, which means that the end result will include both shops and restaurants.\n", "\n", "Negative filters require all passed conditions to be satisfied, so every feature in the end result will have no defined opening hours or website."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_pbf_to_geodataframe(\n", "    monaco_pbf_file, tags_filter=tags_filter, keep_all_tags=True, verbosity_mode=\"silent\"\n", ").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Wildcard filters\n", "\n", "The second additional functionality, in addition to the negative filters, is the use of wildcard characters in matching text values. The mechanism works for both keys and values of the OSM tag and can be combined with the negative filters described above.\n", "\n", "To include a **wildcard**, write an asterisk (`*`) in the tags filter. Under the hood, **QuackOSM** will scan the whole dataset to find matching OSM tags and expand them to the full SQL query."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Features with any of the possible address related tags\n", "\n", "OSM tags are often categorized with a prefix, followed by a colon and a postfix. One of these prefixes is [`addr:`](https://taginfo.openstreetmap.org/search?q=addr%3A#keys).\n", "\n", "Here the OSM tags filter will be expanded into all possible tag values and return features with at least one of those tags defined."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"addr:*\": True}\n", "convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Buildings without any of the address related tags\n", "\n", "Similarly to the previous example, now we will try to find all of the building without any `addr:` tags.\n", "\n", "This query can be useful for finding objects that are missing some features and could be defined better."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"building\": True, \"addr:*\": False}\n", "convert_pbf_to_geodataframe(\n", "    monaco_pbf_file, tags_filter=tags_filter, keep_all_tags=True, verbosity_mode=\"silent\"\n", ").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Select highway objects ending with `-ary`\n", "\n", "There are few `highway` tag values that end with `-ary`: primary, secondary, tertiary. We can find all of them with one wildcard query."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"highway\": \"*ary\"}\n", "convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Using wildcard in OSM tag key and value at once\n", "\n", "Here we will try to find all features related to the Monaco Grand Prix in multiple languages at once (`name:` tags)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tags_filter = {\"name:*\": \"*Grand Prix*\"}\n", "convert_pbf_to_geodataframe(\n", "    monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\"\n", ").sort_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invalid filters\n", "\n", "The combination of positive and negative filters linked to the same OSM tag can lead to contradictions. When this occurs, an error will be thrown.\n", "\n", "Such a contradiction may occur when parsing a `GroupedOsmTagsFilter` with one group having positive tag filter and second one having negative filter. Another situation in which this may occur is the combination of a wildcard which, when expanded, will overlap with another existing tag.\n", "\n", "Thrown error will contain OSM tag key and values that are in conflict."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    tags_filter = {\"group_1\": {\"amenity\": \"bench\"}, \"group_2\": {\"amenity\": False}}\n", "    convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\").sort_index()\n", "except ValueError as ex:\n", "    print(ex)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    tags_filter = {\"name:en\": True, \"name:*\": False}\n", "    convert_pbf_to_geodataframe(monaco_pbf_file, tags_filter=tags_filter, verbosity_mode=\"silent\").sort_index()\n", "except ValueError as ex:\n", "    print(ex)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}