{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# QuackOSM Command Line Interface\n", "\n", "**QuackOSM** contains a CLI for users convenience. It is **not** installed by default when installed using `pip install quackosm`.\n", "\n", "To include the CLI, **QuackOSM** has to be installed with additional group called `cli`: `pip install quackosm[cli]`.\n", "\n", "CLI is based on the `typer` library and exposes almost all of the features implemented in the Python API.\n", "\n", "After installation, the `QuackOSM` (or `quackosm`) command will be available in the shell.\n", "\n", "Each command error returns a verbose description what went wrong."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extend the default console width from 80 characters\n", "import os\n", "\n", "os.environ[\"COLUMNS\"] = \"160\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic usage\n", "\n", "By default, the quackosm requires just the path to the `PBF` file. Without it, there will be an error."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! QuackOSM"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's download a small extract and test the basic usage.\n", "\n", "Because we are passing an URL, QuackOSM will download it automatically and save it in the `files` directory."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM https://download.geofabrik.de/europe/andorra-latest.osm.pbf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Second execution of this command will immediately return a path to the previously generated file.\n", "\n", "Since the file is already downloaded, we can use it directly."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM files/andorra-latest.osm.pbf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To force the regeneration of the GeoParquet file, add the `--ignore-cache` flag (or `--no-cache`) to the command."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM files/andorra-latest.osm.pbf --ignore-cache"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Help command\n", "\n", "To get the full description of all arguments of the QuackOSM command, you can use the `--help` (or `-h`) parameter."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM --help"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Geometry filters\n", "\n", "QuackOSM can automatically download required PBF files based on multiple geometry filters:\n", "- Text to geocode using Nominatim\n", "- WKT geometry\n", "- GeoJSON geometry\n", "- Geometry file path\n", "- H3 spatial index\n", "- Geohash spatial index\n", "- S2 spatial index\n", "\n", "These filters can also be used to filter out geometries from provided pbf file.\n", "\n", "`QuackOSM` will raise an error if provided geometry has parts without area (such as Points, LineStrings or empty geometry)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see the example based on Monaco region.\n", "\n", "First, we will visualise multiple filters on the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import geopandas as gpd\n", "\n", "from quackosm.cli import (\n", "    GeocodeGeometry<PERSON><PERSON><PERSON>,\n", "    Geohash<PERSON>eo<PERSON><PERSON><PERSON><PERSON>,\n", "    GeoJsonGeometry<PERSON><PERSON><PERSON>,\n", "    H3Geometry<PERSON><PERSON><PERSON>,\n", "    S2Geometry<PERSON><PERSON><PERSON>,\n", "    WktGeometry<PERSON><PERSON><PERSON>,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["geocode_string = \"Monaco-Ville, Monaco\"\n", "geojson_string = \"\"\"{\"type\":\"Feature\",\"geometry\":{\"coordinates\":[[[7.416,43.734],[7.416,43.731],[7.421,43.731],[7.421,43.734],[7.416,43.734]]],\"type\":\"Polygon\"}}\"\"\"\n", "wkt_string = \"POLYGON ((7.414 43.735, 7.414 43.732, 7.419 43.732, 7.419 43.735, 7.414 43.735))\"\n", "h3_string = \"893969a4037ffff\"\n", "geohash_string = \"spv2bcs\"\n", "s2_string = \"12cdc28dc\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["geometry_types = [\"Geocode\", \"GeoJSON\", \"WKT\", \"H3\", \"GeoHash\", \"S2\"]\n", "geometries = [\n", "    GeocodeGeometryParser().convert(geocode_string),\n", "    GeoJsonGeometryParser().convert(geojson_string),\n", "    WktGeometryParser().convert(wkt_string),\n", "    H3GeometryParser().convert(h3_string),\n", "    GeohashGeometryParser().convert(geohash_string),\n", "    S2GeometryParser().convert(s2_string),\n", "]\n", "gpd.GeoDataFrame(\n", "    data=dict(type=geometry_types),\n", "    geometry=geometries,\n", "    crs=4326,\n", ").explore(column=\"type\", tiles=\"CartoDB positron\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we will execute each filter and let QuackOSM find required region on its own.\n", "\n", "During first execution, QuackOSM will cache three PBF files sources locally. This operation takes some time.\n", "\n", "The `--silent` flag will disable the progress output to the terminal."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Geocoding"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM --geom-filter-geocode 'Monaco-Ville, Monaco' --silent --output files/geocode_example.parquet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### GeoJSON"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM --geom-filter-geojson '{\"type\":\"Feature\",\"geometry\":{\"coordinates\":[[[7.416,43.734],[7.416,43.731],[7.421,43.731],[7.421,43.734],[7.416,43.734]]],\"type\":\"Polygon\"}}' --silent --output files/geojson_example.parquet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM --geom-filter-index-geohash spv2bcs --silent --output files/geohash_example.parquet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### H3"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM --geom-filter-index-h3 893969a4037ffff --silent --output files/h3_example.parquet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### S2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM --geom-filter-index-s2 12cdc28dc --silent --output files/s2_example.parquet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### WKT"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM --geom-filter-wkt 'POLYGON ((7.414 43.735, 7.414 43.732, 7.419 43.732, 7.419 43.735, 7.414 43.735))' --silent --output files/wkt_example.parquet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Plot all results for comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "fig, axs = plt.subplots(2, 3, sharex=True, sharey=True, figsize=(10, 6))\n", "\n", "for idx, (geometry_type, geometry) in enumerate(zip(geometry_types, geometries)):\n", "    ax = axs[idx // 3, idx % 3]\n", "    gdf = gpd.read_parquet(f\"files/{geometry_type.lower()}_example.parquet\")\n", "    gdf.plot(ax=ax, markersize=1, zorder=1, alpha=0.8)\n", "    gdf.boundary.plot(ax=ax, markersize=0, zorder=1, alpha=0.8)\n", "    gpd.GeoSeries([geometry], crs=4326).plot(\n", "        ax=ax,\n", "        color=(0, 0, 0, 0),\n", "        zorder=2,\n", "        hatch=\"///\",\n", "        edgecolor=\"orange\",\n", "        linewidth=1.5,\n", "    )\n", "    ax.set_title(geometry_type)\n", "\n", "fig.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## OSM tags filters\n", "\n", "By default, QuackOSM parses all of the features (nodes, ways, relations) from the `*.osm.pbf` file with tags attached.\n", "\n", "Hovewer, there is also an option to pass an OSM tags filter in the form of JSON string or path to the JSON file.\n", "\n", "OSM tags filter logic is based on the filter from the [`OSMnx`](https://github.com/gboeing/osmnx) library.\n", "\n", "Filter is expected to be in the form of dictionary with `keys` as string and `values` as one of the types: string, list of strings or bool value. Full tutorial for OSM tags filters can be accessed [here](../advanced_examples/osm_tags_filter).\n", "\n", "Example filters:\n", "\n", "- All of the buildings\n", "  ```json\n", "  { \"building\": true }\n", "  ```\n", "- Parkings and offices\n", "  ```json\n", "  {\n", "    \"amenity\": \"parking\",\n", "    \"building\": \"office\"\n", "  }\n", "  ```\n", "- General shops\n", "  ```json\n", "  {\n", "    \"shop\": [\n", "      \"convenience\",\n", "      \"department_store\",\n", "      \"general\",\n", "      \"kiosk\",\n", "      \"mall\",\n", "      \"supermarket\",\n", "      \"wholesale\"\n", "    ]\n", "  }\n", "  ```\n", "\n", "Tags filters can be used together with geometry filters to get specific features from the area of interest."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"admonition info\">\n", "    <p class=\"admonition-title\">Info</p>\n", "    <p>\n", "    By default, without any tags filters, QuackOSM returns all of the tags of the feature grouped as a single column: <strong>tags</strong>.<br />\n", "    With tags filter, result file will keep the used in the filter and keep each tag key as a separate column.<br />\n", "    To keep all tags while filtering the file, use <strong>--keep-all-tags</strong> flag.\n", "    </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM files/andorra-latest.osm.pbf --osm-tags-filter '{ \"building\": true }'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Keeping tags compact or separate\n", "\n", "QuackOSM can keep tags in the compact form (as a single column named `tags`) or wide form (each tag key as separate column).\n", "If not set by the user, it will change depending on the presence of tags filter:\n", "- without tags filter: tags kept together as a sinlge column\n", "- with tags filter: tags split into separate columns\n", "\n", "User can force one of those two behaviours regardless of osm tags filter being present or not:\n", "- `--explode-tags` (or `--explode`): will always split tags into separate columns, sometimes resulting in hundreds or event thousands of columns in the result file.\n", "- `--compact-tags` (or `--compact`): will always keep tags together as a single column."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Separated tags (`explode`)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM files/andorra-latest.osm.pbf --osm-tags-filter '{ \"amenity\": \"parking\", \"building\": \"office\" }' --explode --output files/andorra_filtered_exploded.parquet --silent"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! ./duckdb :memory: \"FROM read_parquet('files/andorra_filtered_exploded.parquet')\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Compact tags (`compact`)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM files/andorra-latest.osm.pbf --osm-tags-filter '{ \"amenity\": \"parking\", \"building\": \"office\" }' --compact --output files/andorra_filtered_compact.parquet --silent"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! ./duckdb :memory: \"FROM read_parquet('files/andorra_filtered_compact.parquet')\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## WKT mode\n", "\n", "By default, QuackOSM saves parsed files in the `GeoParquet` format with the geometry in the `WKB` format.\n", "\n", "There is also an option to save the file as a `Parquet` file with the geometry in the `WKT` format using `--wkt-result` (or `--wkt`) parameter."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! QuackOSM files/andorra-latest.osm.pbf --wkt-result --silent"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["! ./duckdb :memory: \"FROM read_parquet('files/andorra-latest_nofilter_noclip_compact_wkt.parquet')\""]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}