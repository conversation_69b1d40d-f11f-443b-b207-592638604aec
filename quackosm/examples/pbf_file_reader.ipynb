{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# PBF File Reader\n", "\n", "`PBFFileReader` can really quickly parse full OSM extract in the form of `*.osm.pbf` file.\n", "\n", "It uses `DuckDB` with `spatial` extension to convert `pbf` files into `geoparquet` files without GDAL dependency.\n", "\n", "Reader can filter objects by geometry and by OSM tags with option to split tags into columns or keep it as a single dictionary.\n", "\n", "Caching strategy is implemented to reduce computations, but it can be overriden using `ignore_cache` parameter."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download all buildings in Reykjavík, Iceland\n", "\n", "Filtering the data with geometry and by tags, with tags in exploded form"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from quackosm import PbfFileReader\n", "import urllib.request\n", "import osmnx as ox"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["iceland_pbf_url = \"https://download.geofabrik.de/europe/iceland-latest.osm.pbf\"\n", "iceland_pbf_file = \"iceland.osm.pbf\"\n", "urllib.request.urlretrieve(iceland_pbf_url, iceland_pbf_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reykjavik_gdf = ox.geocode_to_gdf(\"Reykjavík, IS\")\n", "reykjavik_gdf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To filter out buildings, we will utilize format used also in the `osmnx` library: a dictionary with keys representing tag keys and values that could be a bool, string or a list of string.\n", "\n", "By default, `QuackOSM` will return only the tags that are present in the passed filter.\n", "\n", "In this example we will select all the buildings using `{ \"building\": True }` filter and only `building` tag values will be present in the result."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reader = PbfFileReader(\n", "    geometry_filter=reykjavik_gdf.geometry.iloc[0], tags_filter={\"building\": True}\n", ")\n", "\n", "reykjavik_buildings_gpq = reader.convert_pbf_to_parquet(\"iceland.osm.pbf\")\n", "reykjavik_buildings_gpq"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Read those features using DuckDB"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import duckdb\n", "\n", "connection = duckdb.connect()\n", "\n", "connection.load_extension(\"parquet\")\n", "connection.load_extension(\"spatial\")\n", "\n", "features_relation = connection.read_parquet(str(reykjavik_buildings_gpq)).project(\n", "    \"* REPLACE (ST_GeomFromWKB(geometry) AS geometry)\"\n", ")\n", "features_relation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Count all buildings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features_relation.count(\"feature_id\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Keeping all the tags while filtering the data\n", "\n", "To keep all of the tags present in the source data, we can use `keep_all_tags` parameter. That way we will still return only buildings, but with all of the tags attached. \n", "\n", "By default, all of those tags will be kept in a single column as a `dict`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reader.convert_pbf_to_geodataframe(\"iceland.osm.pbf\", keep_all_tags=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download main roads for Estonia\n", "Filtering the data only by tags, with tags in exploded form"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["highways_filter = {\n", "    \"highway\": [\n", "        \"motorway\",\n", "        \"trunk\",\n", "        \"primary\",\n", "        \"secondary\",\n", "        \"tertiary\",\n", "    ]\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["estonia_pbf_url = \"http://download.geofabrik.de/europe/estonia-latest.osm.pbf\"\n", "estonia_pbf_file = \"estonia.osm.pbf\"\n", "urllib.request.urlretrieve(estonia_pbf_url, estonia_pbf_file)\n", "\n", "reader = PbfFileReader(geometry_filter=None, tags_filter=highways_filter)\n", "estonia_features_gpq = reader.convert_pbf_to_parquet(estonia_pbf_file)\n", "estonia_features_gpq"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features_relation = connection.read_parquet(str(estonia_features_gpq)).project(\n", "    \"* REPLACE (ST_GeomFromWKB(geometry) AS geometry)\"\n", ")\n", "features_relation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Count loaded roads"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features_relation.count(\"feature_id\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calculate roads length\n", "We will transform the geometries to the Estonian CRS - [EPSG:3301](https://epsg.io/3301)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["length_in_meters = (\n", "    features_relation.project(\n", "        \"ST_Length(ST_Transform(geometry, 'EPSG:4326', 'EPSG:3301')) AS road_length\"\n", "    )\n", "    .sum(\"road_length\")\n", "    .fetchone()[0]\n", ")\n", "length_in_km = length_in_meters / 1000\n", "length_in_km"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot the roads using GeoPandas\n", "\n", "With fast loading of geoparquet files using `geoarrow.pyarrow` library."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import geoarrow.pyarrow as ga\n", "from geoarrow.pyarrow import io\n", "\n", "from quackosm._constants import GEOMETRY_COLUMN\n", "\n", "parquet_table = io.read_geoparquet_table(estonia_features_gpq)\n", "ga.to_geopandas(parquet_table.column(GEOMETRY_COLUMN)).plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Download all data for Liechtenstein\n", "Without filtering, with tags in a compact form"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["liechtenstein_pbf_url = \"https://download.geofabrik.de/europe/liechtenstein-latest.osm.pbf\"\n", "liechtenstein_pbf_file = \"liechtenstein.osm.pbf\"\n", "urllib.request.urlretrieve(liechtenstein_pbf_url, liechtenstein_pbf_file)\n", "\n", "# Here explode_tags is set to False explicitly,\n", "# but it would set automatically when not filtering the data\n", "reader = PbfFileReader(geometry_filter=None, tags_filter=None)\n", "liechtenstein_features_gpq = reader.convert_pbf_to_parquet(\n", "    liechtenstein_pbf_file, explode_tags=False\n", ") \n", "liechtenstein_features_gpq"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features_relation = connection.read_parquet(str(liechtenstein_features_gpq)).project(\n", "    \"* REPLACE (ST_GeomFromWKB(geometry) AS geometry)\"\n", ")\n", "features_relation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Return data as GeoDataFrame\n", "\n", "`PbfFileReader` can also return the data in the GeoDataFrame form.\n", "\n", "Here the caching strategy will be utilized - file won't be transformed again."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features_gdf = reader.convert_pbf_to_geodataframe(liechtenstein_pbf_file)\n", "features_gdf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot the forests using GeoPandas\n", "\n", "Filter all polygons and features with `landuse`=`forest`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features_gdf[\n", "    features_gdf.geom_type.isin((\"Polygon\", \"MultiPolygon\"))\n", "    & features_gdf.tags.apply(lambda x: \"landuse\" in x and x[\"landuse\"] == \"forest\")\n", "].plot(color=\"green\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "vscode": {"interpreter": {"hash": "4153976b658cb8b76d04b10dc7a0c871c2dac1d3dcfe690ad61d83a61969a12e"}}}, "nbformat": 4, "nbformat_minor": 2}