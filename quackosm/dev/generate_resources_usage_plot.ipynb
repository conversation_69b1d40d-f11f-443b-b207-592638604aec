{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import tempfile\n", "import time\n", "from multiprocessing.pool import ThreadPool\n", "from pathlib import Path\n", "from typing import cast\n", "\n", "import matplotlib.pyplot as plt\n", "import matplotlib.ticker as tkr\n", "import numpy as np\n", "import psutil\n", "import rich\n", "import seaborn as sns\n", "from adjustText import adjust_text\n", "from cpuinfo import get_cpu_info\n", "from pooch import retrieve\n", "from psutil._common import bytes2human\n", "from rich.console import Console\n", "\n", "import quackosm as qosm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sns.set_theme(style=\"darkgrid\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_cpu_cores(p: psutil.Process) -> tuple[float, float]:\n", "    \"\"\"Get CPU usage.\"\"\"\n", "    while True:\n", "        try:\n", "            return (\n", "                (\n", "                    p.cpu_percent(interval=None)\n", "                    + sum(_pc.cpu_percent(interval=None) for _pc in p.children(recursive=True))\n", "                )\n", "                / 100,\n", "                round(time.time(), 2),\n", "            )\n", "        except:\n", "            pass\n", "\n", "\n", "def get_cpu_percentage(p: psutil.Process) -> tuple[float, float]:\n", "    \"\"\"Get CPU usage.\"\"\"\n", "    while True:\n", "        try:\n", "            return (\n", "                (\n", "                    p.cpu_percent(interval=None)\n", "                    + sum(_pc.cpu_percent(interval=None) for _pc in p.children(recursive=True))\n", "                )\n", "                / psutil.cpu_count(),\n", "                round(time.time(), 2),\n", "            )\n", "        except:\n", "            pass\n", "\n", "\n", "def get_memory_percentage(p: psutil.Process) -> tuple[float, float]:\n", "    \"\"\"Get RAM usage.\"\"\"\n", "    while True:\n", "        try:\n", "            return (\n", "                p.memory_percent()\n", "                + sum(_pc.memory_percent() for _pc in p.children(recursive=True)),\n", "                round(time.time(), 2),\n", "            )\n", "        except:\n", "            pass\n", "\n", "\n", "def get_memory_bytes_size(p: psutil.Process) -> tuple[float, float]:\n", "    \"\"\"Get RAM usage.\"\"\"\n", "    while True:\n", "        try:\n", "            return (p.memory_full_info().rss, round(time.time(), 2))\n", "        except:\n", "            pass\n", "\n", "\n", "def get_directory_bytes_size(directory: Path) -> tuple[float, float]:\n", "    \"\"\"Get directory size in bytes.\"\"\"\n", "    while True:\n", "        try:\n", "            return (\n", "                sum(f.stat().st_size for f in Path(directory).rglob(\"*\")),\n", "                round(time.time(), 2),\n", "            )\n", "        except:\n", "            pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GLOBAL_CONSOLE = None\n", "\n", "\n", "def _get_rich_console_new(stderr: bool = False) -> Console:\n", "    global GLOBAL_CONSOLE  # noqa: PLW0603\n", "    GLOBAL_CONSOLE = Console(record=True, stderr=stderr)\n", "    return GLOBAL_CONSOLE\n", "\n", "\n", "rich.get_console = _get_rich_console_new"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def _execute_example(pbf_path, working_directory) -> tuple[Path, dict]:\n", "    path = qosm.convert_pbf_to_parquet(\n", "        pbf_path=pbf_path,\n", "        working_directory=working_directory,\n", "        ignore_cache=True,\n", "        verbosity_mode=\"silent\",\n", "        debug_times=True,\n", "    )\n", "    output_text = cast(<PERSON><PERSON><PERSON>, GLOBAL_CONSOLE).export_text()\n", "    search_text = \"Steps times: \"\n", "    times = json.loads(output_text[(len(search_text) - 1) :])\n", "    return path, times\n", "\n", "\n", "def _sizeof_fmt(x, pos):\n", "    if x < 0:\n", "        return \"\"\n", "    return bytes2human(x)\n", "\n", "\n", "def run_example(example_name: str, pbf_download_url: str) -> None:\n", "    \"\"\"Run example and monitor usage.\"\"\"\n", "    memory_values = []\n", "    cpu_values = []\n", "    disk_values = []\n", "\n", "    downloaded_file = retrieve(url=pbf_download_url, known_hash=None)\n", "\n", "    Path(\"files\").mkdir(parents=True, exist_ok=True)\n", "    p = psutil.Process()\n", "\n", "    with tempfile.TemporaryDirectory(dir=\"files\") as tmp_dir_name:\n", "        start_memory, start_time = get_memory_bytes_size(p)\n", "        memory_values.append(get_memory_bytes_size(p))\n", "        cpu_values.append(get_cpu_cores(p))\n", "        disk_values.append(get_directory_bytes_size(tmp_dir_name))\n", "\n", "        time.sleep(0.1)\n", "\n", "        memory_values.append(get_memory_bytes_size(p))\n", "        cpu_values.append(get_cpu_cores(p))\n", "        disk_values.append(get_directory_bytes_size(tmp_dir_name))\n", "\n", "        with ThreadPool() as pool:\n", "            r = pool.apply_async(_execute_example, args=(downloaded_file, tmp_dir_name))\n", "            while not r.ready():\n", "                memory_values.append(get_memory_bytes_size(p))\n", "                cpu_values.append(get_cpu_cores(p))\n", "                disk_values.append(get_directory_bytes_size(tmp_dir_name))\n", "            result_gpq_file, steps_times = r.get()\n", "            print(result_gpq_file)\n", "            gpq_file_size = Path(result_gpq_file).stat().st_size\n", "\n", "    time.sleep(0.1)\n", "\n", "    memory_values.append(get_memory_bytes_size(p))\n", "    cpu_values.append(get_cpu_cores(p))\n", "    disk_values.append(get_directory_bytes_size(tmp_dir_name))\n", "\n", "    operation_start_time = min(steps_times.values())\n", "\n", "    memory_values_adjusted = [\n", "        (val - start_memory, ts - operation_start_time)\n", "        for val, ts in memory_values\n", "        if ts >= (operation_start_time - 0.1)\n", "    ]\n", "    cpu_values_adjusted = [\n", "        (val, ts - operation_start_time)\n", "        for val, ts in cpu_values\n", "        if ts >= (operation_start_time - 0.1)\n", "    ]\n", "    disk_values_adjusted = [\n", "        (val, ts - operation_start_time)\n", "        for val, ts in disk_values\n", "        if ts >= (operation_start_time - 0.1)\n", "    ]\n", "\n", "    fig = plt.figure(figsize=(20, 10))\n", "    gs = fig.add_gridspec(nrows=4, ncols=1, height_ratios=[1, 1, 1, 1.5])\n", "    ax_0 = fig.add_subplot(gs[0])\n", "    ax_1 = fig.add_subplot(gs[1], sharex=ax_0)\n", "    ax_2 = fig.add_subplot(gs[2], sharex=ax_0)\n", "    ax_3 = fig.add_subplot(gs[3], sharex=ax_0)\n", "\n", "    plt.setp(ax_0.get_xticklabels(), visible=False)\n", "    plt.setp(ax_1.get_xticklabels(), visible=False)\n", "    plt.setp(ax_2.get_xticklabels(), visible=False)\n", "    plt.setp(ax_3.get_yticklabels(), visible=False)\n", "\n", "    mem_val, mem_times = zip(*memory_values_adjusted)\n", "    cpu_val, cpu_times = zip(*cpu_values_adjusted)\n", "    dsk_val, dsk_times = zip(*disk_values_adjusted)\n", "\n", "    sns.lineplot(x=cpu_times, y=cpu_val, ax=ax_0)\n", "    sns.lineplot(x=mem_times, y=mem_val, ax=ax_1)\n", "    sns.lineplot(x=dsk_times, y=dsk_val, ax=ax_2)\n", "\n", "    pbf_file_size = Path(downloaded_file).stat().st_size\n", "    ax_2.hlines(y=pbf_file_size, xmin=dsk_times[0], xmax=dsk_times[-1], color=\"r\", linestyles=\"--\")\n", "\n", "    ax_2.annotate(\n", "        f\"PBF file size ({bytes2human(pbf_file_size)})\",\n", "        xy=(dsk_times[-1] / 2, pbf_file_size),\n", "        xytext=(0, -8),\n", "        textcoords=\"offset points\",\n", "        ha=\"center\",\n", "        va=\"top\",\n", "    )\n", "\n", "    ax_2.hlines(\n", "        y=gpq_file_size, xmin=dsk_times[0], xmax=dsk_times[-1], color=\"orange\", linestyles=\"--\"\n", "    )\n", "\n", "    ax_2.annotate(\n", "        f\"Result file size ({bytes2human(gpq_file_size)})\",\n", "        xy=(dsk_times[-1] / 2, gpq_file_size),\n", "        xytext=(0, 4),\n", "        textcoords=\"offset points\",\n", "        ha=\"center\",\n", "        va=\"bottom\",\n", "    )\n", "    steps = [0.15, -0.15, 0.35, -0.35, 0.55, -0.55, 0.75, -0.75, 0.95, -0.95]\n", "    levels = np.tile(steps, int(np.ceil(len(steps_times) / len(steps))))[: len(steps_times)]\n", "\n", "    texts = []\n", "    times = []\n", "    for (step_name, timestamp), level in zip(steps_times.items(), levels):\n", "        time_s = timestamp - operation_start_time\n", "        ax_3.vlines(\n", "            x=time_s,\n", "            ymin=0,\n", "            ymax=level,\n", "            alpha=0.4,\n", "        )\n", "        ax_3.plot(time_s, level, \"-o\", markerfacecolor=\"w\", markersize=1)\n", "        texts.append(plt.text(time_s, level, step_name))\n", "\n", "    times = ax_3.plot(\n", "        [ts - operation_start_time for ts in steps_times.values()],\n", "        [0 for _ in steps_times.values()],\n", "        \"-o\",\n", "        markerfacecolor=\"w\",\n", "    )\n", "\n", "    adjust_text(\n", "        texts,\n", "        arrowprops=dict(arrowstyle=\"->\", color=\"k\", lw=1),\n", "        objects=times,\n", "        avoid_self=False,\n", "        pull_threshold=1000,\n", "        expand=(1, 1),\n", "    )\n", "\n", "    ax_3.set_ylim(-1, 1)\n", "\n", "    ax_1.yaxis.set_major_formatter(tkr.<PERSON><PERSON><PERSON>ormatter(_sizeof_fmt))\n", "    ax_2.yaxis.set_major_formatter(tkr.<PERSON><PERSON><PERSON>ormatter(_sizeof_fmt))\n", "\n", "    ax_0.set_ylabel(\"CPU usage (threads)\")\n", "    ax_1.set_ylabel(\"Memory usage\")\n", "    ax_2.set_ylabel(\"Disk usage\")\n", "    ax_3.set_ylabel(\"Steps\")\n", "\n", "    ax_3.set_xlabel(\"Time (s)\")\n", "\n", "    plt.suptitle(f\"Resources usage - {example_name}.osm.pbf file\")\n", "\n", "    plt.tight_layout()\n", "\n", "    assets_path = Path(\".\").resolve().parent / \"docs\" / \"assets\" / \"images\"\n", "\n", "    plt.savefig(assets_path / f\"{example_name.lower()}_disk_spillage.png\", bbox_inches=\"tight\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cpu_info = get_cpu_info()\n", "cpu_name = cpu_info[\"brand_raw\"]\n", "cpu_cores = cpu_info[\"count\"]\n", "cpu_freq = cpu_info[\"hz_advertised_friendly\"]\n", "\n", "total_ram = bytes2human(psutil.virtual_memory().total)\n", "\n", "print(f\"{cpu_name} ({cpu_cores} threads, {cpu_freq} clock speed)\\n{total_ram} total memory\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["examples = {\n", "    \"Monaco\": \"https://download.geofabrik.de/europe/monaco-latest.osm.pbf\",\n", "    \"Estonia\": \"https://download.geofabrik.de/europe/estonia-latest.osm.pbf\",\n", "    \"Poland\": \"https://download.geofabrik.de/europe/poland-latest.osm.pbf\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for file_name, url in examples.items():\n", "    run_example(file_name, url)\n", "    # break"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}