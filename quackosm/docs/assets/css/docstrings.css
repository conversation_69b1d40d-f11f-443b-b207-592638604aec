.doc-class > .doc-heading-code,
.doc-function > .doc-heading-code {
  font-size: 1rem !important;
  margin-top: -1em !important;
  display: flex;
}

.doc-class > .doc-heading-code .highlight,
.doc-function > .doc-heading-code .highlight {
  flex-grow: 1;
}

.doc-class > .doc-heading-code a,
.doc-function > .doc-heading-code a {
  margin-top: auto;
  margin-bottom: auto;
}

.doc-class > .doc-labels,
.doc-function > .doc-labels {
  margin-top: -1.2rem;
  display: block;
  font-size: 1.2em;
}

/* Indentation. */
div.doc-contents:not(.first) {
  padding-left: 25px;
  border-left: 0.05rem solid var(--md-typeset-table-color);
}

/* Mark external links as such. */
a.external::after,
a.autorefs-external::after {
  /* https://primer.style/octicons/arrow-up-right-24 */
  mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18.25 15.5a.75.75 0 00.75-.75v-9a.75.75 0 00-.75-.75h-9a.75.75 0 000 1.5h7.19L6.22 16.72a.75.75 0 101.06 1.06L17.5 7.56v7.19c0 .414.336.75.75.75z"></path></svg>');
  -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18.25 15.5a.75.75 0 00.75-.75v-9a.75.75 0 00-.75-.75h-9a.75.75 0 000 1.5h7.19L6.22 16.72a.75.75 0 101.06 1.06L17.5 7.56v7.19c0 .414.336.75.75.75z"></path></svg>');
  content: " ";

  display: inline-block;
  vertical-align: middle;
  position: relative;

  height: 1em;
  width: 1em;
  background-color: var(--md-typeset-a-color);
}

a.external:hover::after,
a.autorefs-external:hover::after {
  background-color: var(--md-accent-fg-color);
}

table.highlighttable td.linenos,
span.linenos,
.highlight .gp,
.highlight .go {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.highlight code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace, SFMono-Regular, Consolas, Menlo,
    monospace;
}
