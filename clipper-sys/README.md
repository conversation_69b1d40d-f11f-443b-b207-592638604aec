# clipper-sys

Unsafe Rust wrapper around C++ version of [Clipper](http://www.angusj.com/delphi/clipper.php).

[![crate.io](https://img.shields.io/crates/v/clipper-sys.svg)](https://crates.io/crates/clipper-sys)
[![docs.rs](https://docs.rs/clipper-sys/badge.svg)](https://docs.rs/clipper-sys)

This library is not meant to be used directly. [geo-clipper](https://github.com/lelongg/geo-clipper) should be used instead.

Compile with cargo feature `generate-bindings` to generate bindings at build time.