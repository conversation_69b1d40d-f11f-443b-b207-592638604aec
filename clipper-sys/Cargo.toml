[package]
name = "clipper-sys"
version = "0.7.2"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
edition = "2018"
license = "ISC"
readme = "README.md"
repository = "https://github.com/lelongg/clipper-sys"
documentation = "https://docs.rs/clipper-sys/"
description = "Boolean operations on polygons (Clipper wrapper)"
keywords = ["polygon", "boolean", "clip", "clipper"]
categories = ["algorithms", "external-ffi-bindings"]

[features]
generate-bindings = [ "bindgen"]
update-bindings = [ "generate-bindings"]

[build-dependencies]
bindgen = { version = "0.70.1", optional = true }
cc = "1.0.59"
