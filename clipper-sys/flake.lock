{"nodes": {"flake-utils": {"locked": {"lastModified": 1637014545, "narHash": "sha256-26IZAc5yzlD9FlDT54io1oqG/bBoyka+FJk5guaX4x4=", "owner": "numtide", "repo": "flake-utils", "rev": "bba5dcc8e0b20ab664967ad83d24d64cb64ec4f4", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flake-utils_2": {"locked": {"lastModified": 1637014545, "narHash": "sha256-26IZAc5yzlD9FlDT54io1oqG/bBoyka+FJk5guaX4x4=", "owner": "numtide", "repo": "flake-utils", "rev": "bba5dcc8e0b20ab664967ad83d24d64cb64ec4f4", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1637841632, "narHash": "sha256-QYqiKHdda0EOnLGQCHE+GluD/Lq2EJj4hVTooPM55Ic=", "owner": "nixos", "repo": "nixpkgs", "rev": "73369f8d0864854d1acfa7f1e6217f7d6b6e3fa1", "type": "github"}, "original": {"owner": "nixos", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1637453606, "narHash": "sha256-Gy6cwUswft9xqsjWxFYEnx/63/qzaFUwatcbV5GF/GQ=", "owner": "NixOS", "repo": "nixpkgs", "rev": "8afc4e543663ca0a6a4f496262cd05233737e732", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"flake-utils": "flake-utils", "nixpkgs": "nixpkgs", "rust-overlay": "rust-overlay"}}, "rust-overlay": {"inputs": {"flake-utils": "flake-utils_2", "nixpkgs": "nixpkgs_2"}, "locked": {"lastModified": 1638065687, "narHash": "sha256-T8s/mgeS74Qtk48HEnZiQxpleekJP6rFPgkffG7imBc=", "owner": "oxalica", "repo": "rust-overlay", "rev": "1cd4ebce6b21f9aa38bfa3d5021135ea5568797f", "type": "github"}, "original": {"owner": "oxalica", "repo": "rust-overlay", "type": "github"}}}, "root": "root", "version": 7}