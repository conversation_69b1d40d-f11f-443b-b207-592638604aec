use std::collections::HashSet;

use actix_web::{web, App, HttpRequest, HttpServer, Responder};
use csa::structures::Timetable;
use serde::Serialize;

use structopt::StructOpt;


#[derive(<PERSON><PERSON><PERSON><PERSON><PERSON>, Debug, <PERSON><PERSON>)]
#[structopt(name = "csa-server", about = "Runs a web server to request routes")]
struct Opt {
    #[structopt(help = "The first day of the timetable")]
    first_day: String,

    #[structopt(
        short = "h",
        long = "horizon",
        help = "How many days are loaded",
        default_value = "1"
    )]
    horizon: u16,

    #[structopt(
        short = "i",
        long = "input",
        help = "Folder where the GTFS files are",
        default_value = "."
    )]
    input: String,
}
#[derive(Serialize, Debug)]
struct Summary{
    departure: chrono::NaiveDateTime,
    arrival: chrono::NaiveDateTime,
    duration: String,
    trips: Vec<usize>,
    stops: Vec<String>,
}


impl Summary{
    fn from(
        connections: &[&csa::structures::Connection],
        timetable: &csa::structures::Timetable,
    ) -> Option<Self> {
        let departure = connections.first()?;//.unwrap_or_else(|| panic!("Missing departure in connection {:?}", connections));
        let arrival = connections.last()?;//.expect("Missing arrival in connexion");
        let trips: Vec<usize> = connections.iter().map(|c| c.trip).collect(); 
        let mut stops: Vec<String> = connections.iter().map(|c| timetable.stops[c.dep_stop].name.clone()).collect();
        stops.push(timetable.stops[arrival.arr_stop].name.to_owned());
        let dep_time = chrono::NaiveTime::from_hms_opt(0, 0, 0).unwrap()
            + chrono::Duration::seconds(departure.dep_time as i64); //chrono::NaiveTime::from_num_seconds_from_midnight(departure.dep_time, 0);
        let arr_time = chrono::NaiveTime::from_hms_opt(0, 0, 0).unwrap()
            + chrono::Duration::seconds(arrival.arr_time as i64);

        let departure = timetable.start_date.and_time(dep_time);
        let arrival = {
            if arr_time >= dep_time {
                timetable.start_date.and_time(arr_time)
            } else {
                timetable.start_date.and_time(arr_time) + chrono::Duration::days(1)
            }
        };
        let duration = arrival - departure;

        // We can filter certain durations (should do this in algo, not here, but yolo)
        if duration > chrono::Duration::seconds(3600) {
            return None
        }
        Some(Self {
            departure,
            arrival,
            duration: duration.num_minutes().to_string(), 
            trips,
            stops
        })
    }
}

async fn compute(req: HttpRequest, timetable: web::Data<Timetable>) -> impl Responder {
    let stop_str = req
        .match_info()
        .get("stop_area")
        .unwrap_or("RDNGSTN");

    let to = timetable.stop_indices_by_stop_str(stop_str);
    let result = csa::algo::compute(&timetable, &to);
    let mut output = Vec::<Vec<_>>::new();
    println!("{:?} stops", timetable.stops.len());

    for i in 0..timetable.stops.len() {
        // println!("Checking result {:?}, len {:?}", i, result[i].len());
        let routes: Vec<Summary> = result[i]
            .iter()
            .filter_map(|profile| Summary::from(
                &profile.route(
                    result.as_slice(),
                    &timetable
                ),
                &timetable
            ))
            .collect();

        output.push(routes);
    }
    serde_json::to_string(&output)
}

#[actix_rt::main]
async fn main() -> std::io::Result<()> {
    let opt = Opt::from_args();
    let gtfs = gtfs_structures::Gtfs::new(&opt.input).unwrap();

    gtfs.print_stats();
    let timetable = Timetable::from_gtfs(&gtfs, &opt.first_day.clone(), opt.horizon);
    // println!("Transfers for first stop: {:?}", timetable.stops.first().unwrap().transfers);
    let data = web::Data::new(timetable);

    HttpServer::new(move || {
        App::new()
            .app_data(data.clone())
            .route("/to/{stop_area}", web::get().to(compute))
    })
    .bind("127.0.0.1:8000")?
    .run()
    .await
}
