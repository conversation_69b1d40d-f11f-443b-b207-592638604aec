extern crate osmpbfreader;
use osmpbfreader::{OsmId, Relation};
use polars::prelude::*;
use itertools::multiunzip;

struct Tag {
    key: String,
    value: String,
}


fn obj_count(filename: &std::ffi::OsStr) -> usize {
    let r = std::fs::File::open(&std::path::Path::new(filename)).unwrap();
    let mut pbf = osmpbfreader::OsmPbfReader::new(r);
    pbf.iter().count()
}


fn to_dfs(filename: &std::ffi::OsStr) {
    let r = std::fs::File::open(&std::path::Path::new(filename)).unwrap();
    let mut pbf = osmpbfreader::OsmPbfReader::new(r);
    
    let mut node_ids: Vec<i64> = Vec::new();
    let mut node_lats = Vec::new();
    let mut node_longs = Vec::new();
    let mut node_tags: Vec<Series> = Vec::new();

    let mut way_ids = Vec::new();
    let mut way_tags: Vec<Series> = Vec::new();
    let mut way_nodes: Vec<Series> = Vec::new();


    let mut relation_ids = Vec::new();
    let mut relation_tags: Vec<Series> = Vec::new();
    let mut relation_refs: Vec<Series> = Vec::new();


    let mut node_df: DataFrame;
    let mut way_df: DataFrame;
    let mut relation_df: DataFrame;
    
    let mut node_file = std::fs::File::create("nodes.parquet").unwrap();
    let mut node_writer = ParquetWriter::new(&mut node_file)
    .with_compression(ParquetCompression::Snappy)
    .batched(&Schema::from_iter(vec![
        Field::new("latitude", DataType::Float64),
        Field::new("longitude", DataType::Float64),
        Field::new("id", DataType::Int64),
        Field::new("tags", DataType::List(Box::new(DataType::Struct(vec![Field { name: "key".into(), dtype: DataType::String }, Field { name: "value".into(), dtype: DataType::String }])))),
    ])).unwrap();

    let mut way_file = std::fs::File::create("ways.parquet").unwrap();
    let mut way_writer = ParquetWriter::new(&mut way_file)
    .with_compression(ParquetCompression::Snappy)
    .batched(&Schema::from_iter(vec![
        Field::new("id", DataType::Int64),
        Field::new("tags", DataType::List(Box::new(DataType::Struct(vec![Field { name: "key".into(), dtype: DataType::String }, Field { name: "value".into(), dtype: DataType::String }])))),
        Field::new("nodes", DataType::List(Box::new(DataType::Int64))),
    ])).unwrap();

    let mut relation_file = std::fs::File::create("relations.parquet").unwrap();
    let mut relation_writer = ParquetWriter::new(&mut relation_file)
    .with_compression(ParquetCompression::Snappy)
    .batched(&Schema::from_iter(vec![
        Field::new("id", DataType::Int64),
        Field::new("tags", DataType::List(Box::new(DataType::Struct(vec![Field { name: "key".into(), dtype: DataType::String }, Field { name: "value".into(), dtype: DataType::String }])))),
        Field::new("relations", DataType::List(Box::new(DataType::Struct(vec![
            Field { name: "member_type".into(), dtype: DataType::String },
            Field { name: "member_id".into(), dtype: DataType::Int64 },
            Field { name: "member_role".into(), dtype: DataType::String }
        ])))),
    ])).unwrap();

    let mut i = 0;
    for obj in pbf.iter().map(Result::unwrap) {
        i+=1;
        match obj {
            osmpbfreader::OsmObj::Node(ref node) => {
                node_ids.push(node.id.0);
                node_lats.push(node.decimicro_lat as f64 * 1E-7);
                node_longs.push(node.decimicro_lon as f64 * 1E-7);
                let (keys, values): (Vec<&str>, Vec<&str>) = node.tags.iter().map(
                    |(key, value)| (key.as_str(), value.as_str())
                ).unzip();
                node_tags.push(
                    DataFrame::new(
                        vec![Series::new("key", keys), Series::new("value", values)]
                    ).unwrap().into_struct("StructChunked").into_series()
                );
            }
            osmpbfreader::OsmObj::Way(ref way) => {
                way_ids.push(way.id.0);
                let (keys, values): (Vec<&str>, Vec<&str>) = way.tags.iter().map(
                    |(key, value)| (key.as_str(), value.as_str())
                ).unzip();
                way_tags.push(
                    DataFrame::new(
                        vec![Series::new("key", keys), Series::new("value", values)]
                    ).unwrap().into_struct("StructChunked").into_series()
                );
                way_nodes.push(Series::new("nodes", way.nodes.iter().map(|x| x.0).collect::<Vec<i64>>()));
            }
            osmpbfreader::OsmObj::Relation(ref rel) => {
                relation_ids.push(rel.id.0);
                let (keys, values): (Vec<&str>, Vec<&str>) = rel.tags.iter().map(
                    |(key, value)| (key.as_str(), value.as_str())
                ).unzip();
                relation_tags.push(
                    DataFrame::new(
                        vec![Series::new("key", keys), Series::new("value", values)]
                    ).unwrap().into_struct("StructChunked").into_series()
                );
                let (types, ids, roles): (Vec<&str>, Vec<i64>, Vec<String>) = multiunzip(rel.refs.iter().map(|r| 
                    {
                        match r.member {
                            OsmId::Node(n) => ("NODE", n.0, r.role.to_string()),
                            OsmId::Way(w) => ("WAY", w.0, r.role.to_string()),
                            OsmId::Relation(re) => ("RELATION", re.0, r.role.to_string())
                        }
                    }
                ));
                relation_refs.push(
                    DataFrame::new(
                        vec![
                            Series::new("member_type", types),
                            Series::new("member_id", ids),
                            Series::new("member_role", roles)
                        ]
                    ).unwrap().into_struct("StructChunked").into_series()
                )
            }
        }
        if i % 1000000 ==0 {
            println!("Processed {:?}", i);
        }
        if node_ids.len() == 512*512 {
            node_df = DataFrame::new(vec![
                Series::new("latitude", node_lats.clone()),
                Series::new("longitude", node_longs.clone()),
                Series::new("id", node_ids.clone()),
                Series::new("tags", node_tags.clone())
                ]
            ).unwrap();

            node_writer.write_batch(&node_df).unwrap();

            node_ids.clear();
            node_lats.clear();
            node_longs.clear();
            node_tags.clear();
        }
        if way_ids.len() == 512*512 {
            println!("Batch of ways");
            way_df = DataFrame::new(vec![
                Series::new("id", way_ids.clone()),
                Series::new("tags", way_tags.clone()),
                Series::new("nodes", way_nodes.clone())
            ]).unwrap();
            way_ids.clear();
            way_tags.clear();
            way_nodes.clear();

            way_writer.write_batch(&way_df).unwrap();
        }
        if relation_ids.len() == 512*512{
            println!("Batch of relations");

            relation_df = DataFrame::new(vec![
                Series::new("id", relation_ids.clone()),
                Series::new("tags", relation_tags.clone()),
                Series::new("relations", relation_refs.clone())
            ]).unwrap();

            relation_writer.write_batch(&relation_df).unwrap();
        }
    }
    // Write final parts of data
    node_df = DataFrame::new(vec![
        Series::new("latitude", node_lats.clone()),
        Series::new("longitude", node_longs.clone()),
        Series::new("id", node_ids.clone()),
        Series::new("tags", node_tags.clone())
        ]
    ).unwrap();
    node_writer.write_batch(&node_df).unwrap();

    way_df = DataFrame::new(vec![
        Series::new("id", way_ids.clone()),
        Series::new("tags", way_tags.clone()),
        Series::new("nodes", way_nodes.clone())
    ]).unwrap();
    way_writer.write_batch(&way_df).unwrap();

    relation_df = DataFrame::new(vec![
        Series::new("id", relation_ids.clone()),
        Series::new("tags", relation_tags.clone()),
        Series::new("relations", relation_refs.clone())
    ]).unwrap();
    relation_writer.write_batch(&relation_df).unwrap();

    println!("Finishing...");
    node_writer.finish().unwrap();
    way_writer.finish().unwrap();
    relation_writer.finish().unwrap();
    println!("Finished");


}

fn main() {
    let args: Vec<_> = std::env::args_os().collect();
    match args.len() {
        2 => {
            let key = args[0].to_str().unwrap();
            // print(obj_count(&args[1]));
            to_dfs(&args[1])
        }
        _ => println!("usage: count filename",),
    };
}