select ST_Transform(ST_BUFFER(ST_Transform(geometry, 'EPSG:4326', 'EPSG:27700'), 0.003), 'EPSG:27700', 'EPSG:4326') as geometry from (
select * from relations_ord
union
select * from ways_ord
where geometry is not null
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
and array_contains(tags['natural'], 'wood')
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;



--- Any tag value
select ST_Transform(ST_BUFFER(ST_Transform(geometry, 'EPSG:4326', 'EPSG:27700'), 0.003), 'EPSG:27700', 'EPSG:4326') as geometry from (
select * from relations_ord
union
select * from ways_ord
where geometry is not null
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
and len(tags['natural']) > 0
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;


select distinct map_keys(tags) from (
select * from relations_ord
union
select * from ways_ord
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;

select key, list_distinct(array_agg(value)) as value from (
select unnest(flatten(array_agg(map_entries(tags))), recursive := true) from (
select * from relations_ord
union
select * from ways_ord
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05
group by 1=1
)
group by key;

select map_from_entries(array_agg(struct_pack(k:= key, v:= value))) from (
select key, list_distinct(array_agg(value)) as value from (
select unnest(flatten(array_agg(map_entries(tags))), recursive := true) from (
select * from relations_ord
union
select * from ways_ord
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05
group by 1=1
)
group by key
)
group by 1=1;
