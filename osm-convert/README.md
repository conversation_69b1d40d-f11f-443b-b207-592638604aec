Convert a .pbf OSM files which contains nodes, ways and relations
Convert it into 3 Parquet files, following similar approach to https://github.com/adrianulbona/osm-parquetizer/tree/master

`cargo run --release path/to/file.pbf`

Ideas:

- Store bounding box information, possibly with hive partitioning
- Switch to Delta instead of just <PERSON><PERSON><PERSON>
- Come up with more typical query cases
- Store geometry information ready to use with map apps, maybe WKB?

Switched to reading OSM data with the duckdb spatial extension and then modifying it via sql queries, much faster than the rust code

14 Oct 2024

After some experiments, `queries_quack.txt` is the summary of going from the OSM .pbf to duckdb databases with the geometries parsed into geometry types


travel types.sql has some more notes
