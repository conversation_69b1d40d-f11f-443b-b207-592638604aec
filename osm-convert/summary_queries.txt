
; Queries to quickly get back to the final result tables from the original OSM PBF

; WIP

; Check out https://github.com/OSGeo/gdal/blob/master/ogr/ogrsf_frmts/osm/data/osmconf.ini
; shows which nodes are not significant, and which kinds of closed ways are supposed to be polygons vs closed lines

CREATE TABLE osm AS
  SELECT * from st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf');


CREATE TABLE nodes AS
  (
    SELECT *, st_point(lon, lat) point from st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
    where kind == 'node'
    order by hilbert_encode([lon, lat]::double[2])
  );


CREATE TABLE ways AS (select * from osm where kind == 'way');




create table way_refs_flat as (
    select id, unnest(osm.refs) as ref from osm where kind == 'way'
);


create table way_lat_lon_flat as (
    select way_refs_flat.id id, way_refs_flat.ref ref, node.lat lat, node.lon lon
    from way_refs_flat
    JOIN nodes node
    ON way_refs_flat.ref == node.id
);


create table ways_lines as (
select id, tags,
CASE WHEN first(ref) == last(ref)
  THEN ST_makeline(list(st_point(lon, lat)))
  ELSE ST_makeline(list(st_point(lon, lat)))
END way
from way_lat_lon_flat
where id < 100000000
group by id
);
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 100000000 and id < 200000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 200000000 and id < 300000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 300000000 and id < 400000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 400000000 and id < 500000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 500000000 and id < 600000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 600000000 and id < 700000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 700000000 and id < 800000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 800000000 and id < 900000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 900000000 and id < 1000000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 1000000000 and id < 1100000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 1100000000 and id < 1200000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 1200000000 and id < 1300000000
group by id;
INSERT INTO ways_points
select id, tags, st_makeline(list(st_point(lon, lat))) way
from way_lat_lon_flat
where id >= 1300000000
group by id;


create table ways_geo as (
    select way_lines.id id, tags, way_lines.line way
    from way_lines
    join ways
    on way_lines.id == ways.id
)



CREATE TABLE ways_g_ord AS
  (
    SELECT * from ways_g
    order by hilbert_encode([st_x(st_centroid(way)), st_y(st_centroid(way))]::double[2])
  );

CREATE TABLE ways_gos AS (
    select *,
    st_xmin(st_extent(way)) as xmin,
    st_ymin(st_extent(way)) as ymin,
    st_xmax(st_extent(way)) as xmax,
    st_ymax(st_extent(way)) as ymax,
    from ways_g_ord
    order by hilbert_encode([xmin, ymin, xmax, ymax]::float[4])
);





; st_read uses GDAL to do more geometry conversion but is apparently slower
; it fails with:
; IO Error: GDAL Error (1): GDALOpen() called on /Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf recursively
select * from st_read('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf', open_options=['INTERLEAVED_READING=YES', 'OSM_CONFIG_FILE=/Users/<USER>/Downloads/osmconf.ini'], layer='lines', sequential_layer_scan=true) limit 10;



;;;;;
; from https://github.com/duckdb/duckdb_spatial/issues/140
; 
WITH elements AS (
    SELECT * FROM ST_ReadOSM('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
),
nodes AS (
    SELECT *, ST_Point(lon, lat) geometry
    FROM elements
    WHERE kind = 'node'
        AND lon IS NOT NULL
        AND lat IS NOT NULL
),
ways AS (
    SELECT *
    FROM elements
    WHERE kind = 'way'
    AND len(refs) > 0
),
relations AS (
    SELECT *
    FROM elements
    WHERE kind = 'relation'
    AND len(refs) > 0
    AND list_contains(map_keys(tags), 'type')
    AND list_has_any(map_extract(tags, 'type'), ['boundary', 'multipolygon'])
),
ways_with_geometries AS (
    SELECT id, ST_MakeLine(list(geometry)) geometry
    FROM (
        SELECT w.*, n.geometry
        FROM (
            SELECT
                id,
                UNNEST(refs) as ref,
                UNNEST(range(length(refs))) as idx,
            FROM ways
        ) w
        JOIN nodes n
        ON n.id = w.ref 
        ORDER BY w.id, w.idx
    )
    GROUP BY id
),
relations_unnested AS (
    SELECT
        *
    FROM (
        SELECT
            id,
            tags,
            UNNEST(refs) as ref,
            UNNEST(ref_types) as ref_type,
            UNNEST(ref_roles) as ref_role,
            UNNEST(range(length(refs))) as idx,
        FROM relations
    )
    WHERE
        ref_type = 'way'
),
relations_with_ways AS (
    SELECT
        r.id, r.ref_type, r.ref_role, r.ref, w.geometry
    FROM relations_unnested r
    JOIN ways_with_geometries w
    ON w.id = r.ref 
    ORDER BY r.id, r.idx
)
-- SELECT COUNT(DISTINCT id) FROM relations -- 1309 rows
SELECT COUNT(DISTINCT id) FROM relations_with_ways -- 1031 rows (278 relations can't be parsed)


;;;
; https://github.com/OSGeo/gdal/blob/master/ogr/ogrsf_frmts/osm/data/osmconf.ini


select * from osm
where kind='node'
and len(list_filter(map_keys(tags), x -> x not in ('created_by', 'converted_by', 'source' ,'time' ,'ele', 'attribution'))) != 0;


select * from osm
where kind='way'
and len(list_filter(map_keys(tags), x -> x not in ('created_by', 'converted_by', 'source' ,'time' ,'ele', 'attribution'))) = 0;

; closed ways which should be treated as polygons
; ['aeroway','amenity','boundary','building','craft','geological','historic','landuse','leisure','military','natural','office','place','shop','sport','tourism','highway=platform','public_transport=platform']


; Closed ways which represent polygons
; 20219313
select * from osm
where kind='way'
and refs[1] = refs[-1] -- The way is closed
-- For these cases, a closed way means it is a polygon
and (
  list_has_any(map_keys(tags), ['aeroway','amenity','boundary','building','craft','geological','historic','landuse','leisure','military','natural','office','place','shop','sport','tourism'])
  or list_has_any(tags['highway'], ['platform', 'services', 'rest_area'])
  or array_contains(tags['public_transport'], 'platform')
  or (array_contains(tags['area'], 'yes'))
)
-- remove cases where area is explicitly 'no'
and not (array_contains(map_keys(tags), 'area') and array_contains(tags['area'], 'no'));




;;;;;;; https://towardsdatascience.com/how-to-read-osm-data-with-duckdb-ffeb15197390
; conclusion: use QuackOSM
; nope, it's way too slow and uses tonnes of memory and disk for some reason. Back to this.



select id, tags, refs, 
case when (
    refs[1] = refs[-1] -- The way is closed
    -- For these cases, a closed way means it is a polygon
    and (
    list_has_any(map_keys(tags), ['aeroway','amenity','boundary','building','craft','geological','historic','landuse','leisure','military','natural','office','place','shop','sport','tourism'])
    or list_has_any(tags['highway'], ['platform', 'services', 'rest_area'])
    or array_contains(tags['public_transport'], 'platform')
    or (array_contains(tags['area'], 'yes'))
    )
    -- remove cases where area is explicitly 'no'
    and not (array_contains(map_keys(tags), 'area') and array_contains(tags['area'], 'no'))

) then TRUE -- polygon

else FALSE -- linestring
end as closed
from osm
where kind='way';

; this works, limiting number of IDs being parsed
WITH way_ref as (
    select id, tags, unnest(ways.refs) r from osm as ways where kind == 'way' and id < 100000000
)
select way_ref.id id, first(way_ref.tags) tags,
list(struct_pack(x:= node.lon::DECIMAL(10, 7), y:= node.lat::DECIMAL(10, 7)))::LINESTRING_2D as way
from way_ref
LEFT JOIN osm node
ON way_ref.r == node.id
group by way_ref.id;


; not tried running this
CREATE TABLE ways_geo AS (
    WITH way_ref as (
        select id, tags, unnest(ways.refs) r from osm as ways where kind == 'way' and id < 100000000
    )
    select way_ref.id id, first(way_ref.tags) tags,
    list(struct_pack(x:= node.lon::DECIMAL(10, 7), y:= node.lat::DECIMAL(10, 7)))::LINESTRING_2D as way
    from way_ref
    LEFT JOIN osm node
    ON way_ref.r == node.id
    group by way_ref.id;
);


-- this runs
WITH way_ref as (
    select id, tags, refs, unnest(ways.refs) r from osm as ways where kind == 'way' and id between 100000000 and 110000000
),
way_points as (
    select way_ref.id id, array_agg(r) rs, first(way_ref.tags) tags,
    list(struct_pack(x:= node.lon::DECIMAL(10, 7), y:= node.lat::DECIMAL(10, 7))) points,
    from way_ref
    LEFT JOIN osm node
    ON way_ref.r == node.id
    where node.lon is not null and node.lat is not null
    group by way_ref.id
),
way_lines as (
    select id, tags, points, 
    case when (
        rs[1] = rs[-1] -- The way is closed
        -- For these cases, a closed way means it is a polygon
        and (
        list_has_any(map_keys(tags), ['aeroway','amenity','boundary','building','craft','geological','historic','landuse','leisure','military','natural','office','place','shop','sport','tourism'])
        or list_has_any(tags['highway'], ['platform', 'services', 'rest_area'])
        or array_contains(tags['public_transport'], 'platform')
        or (array_contains(tags['area'], 'yes'))
        )
        -- remove cases where area is explicitly 'no'
        and not (array_contains(map_keys(tags), 'area') and array_contains(tags['area'], 'no'))

    )
    then true
    else false
    end as closed
    from way_points
)
select id, tags, closed, [points[1:-2]]::POLYGON_2D as polygon, points::LINESTRING_2D as linestring
from way_lines;


select *
from osm a
join (select unnest(refs) r from osm) b
on b.r = a.id
where a.kind = 'way'
limit 10;


