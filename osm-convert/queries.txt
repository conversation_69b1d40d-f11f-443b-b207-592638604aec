; Head of the nodes table
select * from 'nodes.parquet' LIMIT 10;
; Head of the ways table
select * from 'ways.parquet' LIMIT 10;
; Head of the relations table
select * from 'relations.parquet' LIMIT 10;


; Schools in a certain bounding box (around York)
WITH nodes AS (SELECT * FROM 'nodes.parquet' WHERE latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x WHERE key=='school' LIMIT 100;

; All tags on nodes in a certain box (around York)
WITH nodes AS (SELECT * FROM 'nodes.parquet' WHERE latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x LIMIT 100;

; Location of all butchers in a certain box (around York)
WITH nodes AS (SELECT * FROM 'nodes.parquet' WHERE latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x WHERE key=='shop' AND value=='butcher' LIMIT 100;

; cast lat and long to integers, to be able to partition by them
select CAST(longitude as INTEGER) as ilong, CAST(latitude as INTEGER) as ilat, * from 'nodes.parquet' LIMIT 10;

; Try out hive partitioning on lat/long cast as integers
COPY (SELECT CAST(longitude as INTEGER) as ilong, CAST(latitude as INTEGER) as ilat, * FROM 'nodes.parquet') TO 'tst' (FORMAT PARQUET, PARTITION_BY (ilat, ilong));

; Select data using the partition value to speed up a query
WITH nodes AS (SELECT * FROM 'tst/*/*/*.parquet' WHERE ilat BETWEEN 53 and 54 AND ilong BETWEEN -2 AND -1 AND latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x WHERE key=='shop' AND value=='butcher' LIMIT 100;

; Create a Z-ordered index from long and lat to sort by
; long is mapped to 0b010101010101010101
; lat is mapped to 0b101010101010101010
; First, just using lat and long in integer degrees - this doesn't lead to many distinct values
with first as (select
  (CAST(longitude as INTEGER) + 180) as lo,
  (CAST(latitude as INTEGER) + 90) as la,
  *
  from 'nodes.parquet'
)
select
  count(distinct (
    ((lo & 1))
  + ((lo & 2) << 1)
  + ((lo & 4) << 2)
  + ((lo & 8) << 3)
  + ((lo & 16) << 4)
  + ((lo & 32) << 5)
  + ((lo & 64) << 6)
  + ((lo & 128) << 7)
  + ((lo & 256) << 8)
  + ((lo & 512) << 9)
  + ((lo & 1024) << 10)
  + ((lo & 2048) << 11)
  + ((la & 1) << 1)
  + ((la & 2) << 2)
  + ((la & 4) << 3)
  + ((la & 8) << 4)
  + ((la & 16) << 5)
  + ((la & 32) << 6)
  + ((la & 64) << 7)
  + ((la & 128) << 8)
  + ((la & 256) << 9)
  + ((la & 512) << 10)
  + ((la & 1024) << 11)
  + ((la & 2048) << 12)
  ))
  from first
LIMIT 10;


; Putting lat and long back into integer decimicrodegrees (10^-7 deg)
with first as (select
  CAST((longitude+180) * 10**7 as BIGINT) as lo,
  CAST((latitude+90) * 10**7 as BIGINT) as la,
  *
  from 'nodes.parquet'
)
select (
    (lo & 1)
  + ((lo & 2) << 1)
  + ((lo & 4) << 2)
  + ((lo & 8) << 3)
  + ((lo & 16) << 4)
  + ((lo & 32) << 5)
  + ((lo & 64) << 6)
  + ((lo & 128) << 7)
  + ((lo & 256) << 8)
  + ((lo & 512) << 9)
  + ((lo & 1024) << 10)
  + ((lo & 2048) << 11)
  + ((lo & 4096) << 12)
  + ((lo & 8192) << 13)
  + ((lo & 16384) << 14)
  + ((lo & 32768) << 15)
  + ((lo & 65536) << 16)
  + ((lo & 131072) << 17)
  + ((lo & 262144) << 18)
  + ((lo & 524288) << 19)
  + ((lo & 1048576) << 20)
  + ((lo & 2097152) << 21)
  + ((lo & 4194304) << 22)
  + ((lo & 8388608) << 23)
  + ((lo & 16777216) << 24)
  + ((lo & 33554432) << 25)
  + ((lo & 67108864) << 26)
  + ((lo & 134217728) << 27)
  + ((lo & 268435456) << 28)
  + ((lo & 536870912) << 29)
  + ((lo & 1073741824) << 30)
  + ((lo & 2147483648) << 31)
  + ((lo & 4294967296) << 32)

  + ((la & 1) << 1)
  + ((la & 2) << 2)
  + ((la & 4) << 3)
  + ((la & 8) << 4)
  + ((la & 16) << 5)
  + ((la & 32) << 6)
  + ((la & 64) << 7)
  + ((la & 128) << 8)
  + ((la & 256) << 9)
  + ((la & 512) << 10)
  + ((la & 1024) << 11)
  + ((la & 2048) << 12)
  + ((la & 4096) << 13)
  + ((la & 8192) << 14)
  + ((la & 16384) << 15)
  + ((la & 32768) << 16)
  + ((la & 65536) << 17)
  + ((la & 131072) << 18)
  + ((la & 262144) << 19)
  + ((la & 524288) << 20)
  + ((la & 1048576) << 21)
  + ((la & 2097152) << 22)
  + ((la & 4194304) << 23)
  + ((la & 8388608) << 24)
  + ((la & 16777216) << 25)
  + ((la & 33554432) << 26)
  + ((la & 67108864) << 27)
  + ((la & 134217728) << 28)
  + ((la & 268435456) << 29)
  + ((la & 536870912) << 30)
  + ((la & 1073741824) << 31)
  + ((la & 2147483648) << 32)
  + ((la & 4294967296) << 33)
  ) as zind,
  latitude,
  longitude,
  id,
  tags
  from first
LIMIT 10;


; order by zind
COPY (
  with first as (select
    CAST((longitude+180) * 10**7 as BIGINT) as lo,
    CAST((latitude+90) * 10**7 as BIGINT) as la,
    *
    from 'nodes.parquet'
  )
  select (
      (lo & 1)
    + ((lo & 2) << 1)
    + ((lo & 4) << 2)
    + ((lo & 8) << 3)
    + ((lo & 16) << 4)
    + ((lo & 32) << 5)
    + ((lo & 64) << 6)
    + ((lo & 128) << 7)
    + ((lo & 256) << 8)
    + ((lo & 512) << 9)
    + ((lo & 1024) << 10)
    + ((lo & 2048) << 11)
    + ((lo & 4096) << 12)
    + ((lo & 8192) << 13)
    + ((lo & 16384) << 14)
    + ((lo & 32768) << 15)
    + ((lo & 65536) << 16)
    + ((lo & 131072) << 17)
    + ((lo & 262144) << 18)
    + ((lo & 524288) << 19)
    + ((lo & 1048576) << 20)
    + ((lo & 2097152) << 21)
    + ((lo & 4194304) << 22)
    + ((lo & 8388608) << 23)
    + ((lo & 16777216) << 24)
    + ((lo & 33554432) << 25)
    + ((lo & 67108864) << 26)
    + ((lo & 134217728) << 27)
    + ((lo & 268435456) << 28)
    + ((lo & 536870912) << 29)
    + ((lo & 1073741824) << 30)
    + ((lo & 2147483648) << 31)
    + ((lo & 4294967296) << 32)

    + ((la & 1) << 1)
    + ((la & 2) << 2)
    + ((la & 4) << 3)
    + ((la & 8) << 4)
    + ((la & 16) << 5)
    + ((la & 32) << 6)
    + ((la & 64) << 7)
    + ((la & 128) << 8)
    + ((la & 256) << 9)
    + ((la & 512) << 10)
    + ((la & 1024) << 11)
    + ((la & 2048) << 12)
    + ((la & 4096) << 13)
    + ((la & 8192) << 14)
    + ((la & 16384) << 15)
    + ((la & 32768) << 16)
    + ((la & 65536) << 17)
    + ((la & 131072) << 18)
    + ((la & 262144) << 19)
    + ((la & 524288) << 20)
    + ((la & 1048576) << 21)
    + ((la & 2097152) << 22)
    + ((la & 4194304) << 23)
    + ((la & 8388608) << 24)
    + ((la & 16777216) << 25)
    + ((la & 33554432) << 26)
    + ((la & 67108864) << 27)
    + ((la & 134217728) << 28)
    + ((la & 268435456) << 29)
    + ((la & 536870912) << 30)
    + ((la & 1073741824) << 31)
    + ((la & 2147483648) << 32)
    + ((la & 4294967296) << 33)
    ) as zind,
    latitude,
    longitude,
    id,
    tags
    from first
    ORDER BY zind, latitude, longitude
) TO 'zord.parquet' (FORMAT PARQUET);

; select from zordered data
WITH nodes AS (SELECT * FROM 'zord.parquet' WHERE latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x WHERE key=='shop' AND value=='butcher' LIMIT 100;

; butchers and clothes shops
WITH nodes AS (SELECT * FROM 'zord.parquet' WHERE latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x WHERE key=='shop' AND (value=='butcher' or value == 'clothes') LIMIT 100;

; ; partition based on zind? Worth it? Probably not, calculating the z index is longwinded
; bit shifting the z index, how many partitions would we make?
SELECT count(distinct zind >> 10) FROM 'zord.parquet' LIMIT 10;

SELECT (zind >> 51) << 51 as z_shift_51, * FROM 'zord.parquet' LIMIT 10;


; find telephone boxes - takes 0.18 s
WITH nodes AS (SELECT * FROM 'zord.parquet' WHERE latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x WHERE value == 'telephone_box';

; same in 4x the area - takes 0.35 s
WITH nodes AS (SELECT * FROM 'zord.parquet' WHERE latitude > 53.6 AND latitude < 54 AND longitude > -1.4 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x WHERE value == 'telephone_box';

; 4x the area again - takes 1.5 s
WITH nodes AS (SELECT * FROM 'zord.parquet' WHERE latitude > 53.2 AND latitude < 54 AND longitude > -1.8 AND longitude < -1), x AS (SELECT latitude, longitude, id, unnest(tags, recursive := true) FROM nodes) SELECT * FROM x WHERE value == 'telephone_box';

; using list_contains brings the time down to 0.58 s
explain analyze WITH nodes AS (SELECT * FROM 'zord.parquet' WHERE latitude > 53.2 AND latitude < 54 AND longitude > -1.8 AND longitude < -1) SELECT latitude, longitude, id FROM nodes WHERE list_contains(tags, {'key': 'man_made', 'value': 'telephone_box'});

; back to the original size query for phone boxes, this takes 0.11 s
explain analyze SELECT latitude, longitude, id FROM 'zord.parquet' WHERE latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1 AND list_contains(tags, {'key': 'man_made', 'value': 'telephone_box'});

; what about having it all in memory?
CREATE OR REPLACE TABLE zord_mem AS
  SELECT *
  FROM read_parquet('zord.parquet');

; WOW this also still takes 0.1 s, no improvement over reading from disk
explain analyze SELECT latitude, longitude, id FROM zord_mem WHERE latitude > 53.8 AND latitude < 54 AND longitude > -1.2 AND longitude < -1 AND list_contains(tags, {'key': 'man_made', 'value': 'telephone_box'});

; try adding an index to lat,long?
CREATE INDEX s_idx ON zord_mem (latitude, longitude);
; out of memory


;; !! Use the DuckDB spatial plugin https://duckdb.org/docs/extensions/spatial !!
install spatial; load spatial;

; Load PBF directly
SELECT *
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
LIMIT 5;

SELECT COUNT(*)
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf');

SELECT *
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
WHERE kind == 'way'
LIMIT 5;

SELECT *
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
WHERE kind == 'relation'
LIMIT 5;

SELECT *
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
WHERE array_contains(tags['man_made'], 'telephone_box')
LIMIT 5;

select unnest(map_values(tags))
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
limit 5;

select count(distinct (unnest(map_values(tags))))
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf');

; find telephone boxes in area directly from pbf - takes 5.8 s, slower than the equivalent parquet file
SELECT * FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1;

; slight variation
SELECT * FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
WHERE tags['man_made'] = 'telephone_box'
AND lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1;

; Use geometry!
SELECT *, ST_Point(lat, lon) FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
WHERE array_contains(tags['man_made'], 'telephone_box')
limit 5;

; find a way
SELECT *
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
WHERE kind == 'way'
LIMIT 5;


SELECT nodes.lat, nodes.lon, ways.id
FROM st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf') as nodes
JOIN (select id, lat, lon, unnest(refs) as noderef from st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')) as ways
ON nodes.id==ways.noderef
LIMIT 20;


; Load the PBF into a duckdb table then try querying it
; it's too big for an in-memory db: create a db file
.open one.db

CREATE TABLE pbf AS
  SELECT * from st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf');


; query from db file - 0.72 s, still slower than querying the Parquet file. 
SELECT * FROM pbf
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1;

; select tag items
SELECT t FROM (SELECT unnest(map_keys(tags)) as t from pbf WHERE lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1)
WHERE t LIKE '%city%'
LIMIT 100;

; select city
SELECT *, tags['addr:city'][1] as city FROM pbf
WHERE lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1 AND city IS NOT NULL
LIMIT 100;

; select from all data by city!
SELECT * FROM pbf
WHERE tags['addr:city'][1] == 'Reading'
LIMIT 100;

; yo wat
SELECT * FROM pbf
WHERE tags['addr:postcode'][1] == 'RG4 8HY'
LIMIT 100;

; can I find the nags head
SELECT tags, tags['name'][1] as name FROM pbf
WHERE tags['addr:city'][1] == 'Reading' AND name LIKE '%Nag%'
LIMIT 100;

; all pubs in Reading
SELECT tags, tags['name'][1] as name FROM pbf
WHERE tags['addr:city'][1] == 'Reading' AND tags['amenity'][1] == 'pub'
LIMIT 100;

; breweries
SELECT tags, tags['name'][1] as name FROM pbf
WHERE tags['addr:city'][1] == 'Reading' AND tags['brewery'][1] == 'yes'
LIMIT 100;

; buildings with over N levels
SELECT tags, tags['name'][1] as name, try_cast(tags['building:levels'][1] as integer) as levels FROM pbf
WHERE tags['addr:city'][1] == 'Reading' AND levels >4
LIMIT 100;

; extension which does linearization
install lindel from community;
load lindel;


CREATE TABLE pbf_ord AS
  (
    SELECT * from st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf')
    where kind == 'node'
    order by hilbert_encode([lat, lon]::double[2])
  );

COPY (
  select * from st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf');
  where kind == 'node'
  order by
  hilbert_encode([lat, lon]::double[2])
) TO 'example.parquet' (FORMAT PARQUET)


; query from unordered db file - 0.72 s, still slower than querying the Parquet file. 
SELECT * FROM pbf
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1;

; same query from ordered file which also only contains nodes ---- 0.0225 s !!!
; this was 0.18 s for the z-ordered parquet file (also a different column structure, not comparing like with like)
SELECT * FROM pbf_ord
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1;

; this was 1.5 s for z-ordered parquet
; 0.099 s for the db
SELECT * FROM pbf_ord
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat > 53.2 AND lat < 54 AND lon > -1.8 AND lon < -1;

; Compare like with like: copy the db to parquet
COPY pbf_ord to 'hord100k.parquet' (FORMAT 'parquet', COMPRESSION 'snappy', ROW_GROUP_SIZE 100_000);
COPY pbf_ord to 'hord10k.parquet' (FORMAT 'parquet', COMPRESSION 'snappy', ROW_GROUP_SIZE 10_000);
COPY pbf_ord to 'hord250k.parquet' (FORMAT 'parquet', COMPRESSION 'snappy', ROW_GROUP_SIZE 250_000);

; was 0.023 s for the db
; 0.067 s for 250k row group size
; 0.068 s for 100k row group size
; 0.404 s for 10k row group size
explain analyze SELECT * FROM 'hord100k.parquet'
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1;

AND lat > 53.2 AND lat < 54 AND lon > -1.8 AND lon < -1;

; what about limited columns
; pbf_ord: 0.014 s
; 250k row group size: 0.053 s
; 100k row group size: 0.053 s
; 10k row group size: 0.387 s
SELECT lat, lon FROM pbf_ord
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1;

SELECT lat, lon FROM 'hord100k.parquet'
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat > 53.8 AND lat < 54 AND lon > -1.2 AND lon < -1;