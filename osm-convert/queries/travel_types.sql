-- Queries which return ways accessible by certain modes of transport, from 

-- NetworkType::Drive => Ok(
--     "[\"highway\"][\"area\"!~\"yes\"][\"highway\"!~\"abandoned|bridleway|bus_guideway|construction|corridor|cycleway|elevator|escalator|footway|no|path|pedestrian|planned|platform|proposed|raceway|razed|service|steps|track\"][\"motor_vehicle\"!~\"no\"][\"motorcar\"!~\"no\"][\"service\"!~\"alley|driveway|emergency_access|parking|parking_aisle|private\"]"
-- ),
-- NetworkType::DriveService => Ok(
--     "[\"highway\"][\"area\"!~\"yes\"][\"highway\"!~\"abandoned|bridleway|bus_guideway|construction|corridor|cycleway|elevator|escalator|footway|no|path|pedestrian|planned|platform|proposed|raceway|razed|steps|track\"][\"motor_vehicle\"!~\"no\"][\"motorcar\"!~\"no\"][\"service\"!~\"emergency_access|parking|parking_aisle|private\"]"
-- ),
-- NetworkType::Walk => Ok(
--     "[\"highway\"][\"area\"!~\"yes\"][\"highway\"!~\"abandoned|bus_guideway|construction|corridor|elevator|escalator|footway|motor|no|planned|platform|proposed|raceway|razed|steps\"][\"bicycle\"!~\"no\"][\"service\"!~\"private\"]"
-- ),
-- NetworkType::Bike => Ok(
--     "[\"highway\"][\"area\"!~\"yes\"][\"highway\"!~\"abandoned|bus_guideway|construction|corridor|elevator|escalator|footway|motor|no|planned|platform|proposed|raceway|razed|steps\"][\"bicycle\"!~\"no\"][\"service\"!~\"private\"]"
-- ),
-- NetworkType::All => Ok(
--     "[\"highway\"][\"area\"!~\"yes\"][\"highway\"!~\"abandoned|construction|no|planned|platform|proposed|raceway|razed\"][\"service\"!~\"private\"]"
-- ),
-- NetworkType::AllPrivate => Ok(
--     "[\"highway\"][\"area\"!~\"yes\"][\"highway\"!~\"abandoned|construction|no|planned|platform|proposed|raceway|razed\"]"
-- ),

-- general query structures
list_has_any(tags['highway'], ['platform', 'services', 'rest_area']
'platform' in tags['public_transport']
'highway' in tags

-- driving
select tags from ways_ord
where 'highway' in tags
and 'yes' not in tags['area']
and not list_has_any(tags['highway'], ['abandoned', 'bridleway', 'bus_guideway', 'construction', 'corridor', 'cycleway', 'elevator', 'escalator', 'footway', 'no', 'path', 'pedestrian', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'service', 'steps', 'track'])
and not 'no' in tags['motor_vehicle']
and not 'no' in tags['motorcar']
and not list_has_any(tags['service'], ['alley', 'driveway', 'emergency_access', 'parking', 'parking_aisle', 'private'])
limit 10;

-- drive service
select tags from ways_ord
where 'highway' in tags
and 'yes' not in tags['area']
and not list_has_any(tags['highway'], ['abandoned', 'bridleway', 'bus_guideway', 'construction', 'corridor', 'cycleway', 'elevator', 'escalator', 'footway', 'no', 'path', 'pedestrian', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps', 'track'])
and not 'no' in tags['motor_vehicle']
and not 'no' in tags['motorcar']
and not list_has_any(tags['service'], ['emergency_access', 'parking', 'parking_aisle', 'private'])
limit 10;

-- walking
select tags from ways_ord
where 'highway' in tags
and 'yes' not in tags['area']
and not list_has_any(tags['highway'], ['abandoned', 'bus_guideway', 'construction', 'corridor', 'elevator', 'escalator', 'footway', 'motor', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps'])
and not 'no' in tags['bicycle']
and not 'private' in tags['service']
and ST_GeometryType(geometry) == 'LINESTRING'
limit 10;

-- bike
select tags from ways_ord
where 'highway' in tags
and 'yes' not in tags['area']
and not list_has_any(tags['highway'], ['abandoned', 'bus_guideway', 'construction', 'corridor', 'elevator', 'escalator', 'footway', 'motor', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps'])
and not 'no' in tags['bicycle']
and not 'private' in tags['service']
and ST_GeometryType(geometry) == 'LINESTRING'
limit 10;

-- all
select tags from ways_ord
where 'highway' in tags
and 'yes' not in tags['area']
and not list_has_any(tags['highway'], ['abandoned', 'construction', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed'])
and not 'private' in tags['service']
and ST_GeometryType(geometry) == 'LINESTRING'
limit 10;

-- all+private
select tags from ways_ord
where 'highway' in tags
and 'yes' not in tags['area']
and not list_has_any(tags['highway'], ['abandoned', 'construction', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed'])
and ST_GeometryType(geometry) == 'LINESTRING'
limit 10;

-- together as bool columns
with route_ways as (
    select
    *,
    (
        not list_has_any(tags['highway'], ['abandoned', 'bridleway', 'bus_guideway', 'construction', 'corridor', 'cycleway', 'elevator', 'escalator', 'footway', 'no', 'path', 'pedestrian', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'service', 'steps', 'track'])
        and not 'no' in tags['motor_vehicle']
        and not 'no' in tags['motorcar']
        and not list_has_any(tags['service'], ['alley', 'driveway', 'emergency_access', 'parking', 'parking_aisle', 'private'])
    ) as supports_drive,
    (
        not list_has_any(tags['highway'], ['abandoned', 'bridleway', 'bus_guideway', 'construction', 'corridor', 'cycleway', 'elevator', 'escalator', 'footway', 'no', 'path', 'pedestrian', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps', 'track'])
        and not 'no' in tags['motor_vehicle']
        and not 'no' in tags['motorcar']
        and not list_has_any(tags['service'], ['emergency_access', 'parking', 'parking_aisle', 'private'])
    ) as supports_drive_service,
    (
        not list_has_any(tags['highway'], ['abandoned', 'bus_guideway', 'construction', 'corridor', 'elevator', 'escalator', 'footway', 'motor', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps'])
        and not 'no' in tags['bicycle']
        and not 'private' in tags['service']
    ) as supports_walk,
    (
        not list_has_any(tags['highway'], ['abandoned', 'bus_guideway', 'construction', 'corridor', 'elevator', 'escalator', 'footway', 'motor', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps'])
        and not 'no' in tags['bicycle']
        and not 'private' in tags['service']
    ) as supports_bike,
    (
        not list_has_any(tags['highway'], ['abandoned', 'construction', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed'])
        and not 'private' in tags['service']
    ) as supports_all_nonprivate,
    (
        not list_has_any(tags['highway'], ['abandoned', 'construction', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed'])
        and not 'private' in tags['service']
    ) as supports_all_with_private,
    from ways_ord
    where
    'highway' in tags
    and 'yes' not in tags['area']
    and ST_GeometryType(geometry) == 'LINESTRING'
    and (supports_all_with_private or supports_all_nonprivate or supports_bike or supports_walk or supports_drive_service or supports_drive)
)
select w.*, o.refs as node_refs, st_length_spheroid(geometry) as distance, st_length_spheroid(st_flipcoordinates(geometry)) as distance_f
from route_ways w
join osm o using (id)
where o.kind = 'way'
limit 10;

-- one way or two way
-- https://wiki.openstreetmap.org/wiki/Forward_%26_backward,_left_%26_right#Identifying_the_direction_of_a_way
-- oneway=yes (otherwise it's two-way)

create table ways_routes as
with route_ways as (
    select
    *,
    (
        not list_has_any(tags['highway'], ['abandoned', 'bridleway', 'bus_guideway', 'construction', 'corridor', 'cycleway', 'elevator', 'escalator', 'footway', 'no', 'path', 'pedestrian', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'service', 'steps', 'track'])
        and not 'no' in tags['motor_vehicle']
        and not 'no' in tags['motorcar']
        and not list_has_any(tags['service'], ['alley', 'driveway', 'emergency_access', 'parking', 'parking_aisle', 'private'])
    ) as supports_drive,
    (
        not list_has_any(tags['highway'], ['abandoned', 'bridleway', 'bus_guideway', 'construction', 'corridor', 'cycleway', 'elevator', 'escalator', 'footway', 'no', 'path', 'pedestrian', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps', 'track'])
        and not 'no' in tags['motor_vehicle']
        and not 'no' in tags['motorcar']
        and not list_has_any(tags['service'], ['emergency_access', 'parking', 'parking_aisle', 'private'])
    ) as supports_drive_service,
    (
        not list_has_any(tags['highway'], ['abandoned', 'bus_guideway', 'construction', 'corridor', 'elevator', 'escalator', 'footway', 'motor', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps'])
        and not 'private' in tags['service']
        and (
            not list_has_any(tags['sidewalk'], ['None', 'none', 'No', "no"])
            or list_has_any(tags['foot'], ['yes', 'designated', 'permissive'])
        ) -- TODO updated this but haven't rerun it yet
    ) as supports_walk,
    (
        not list_has_any(tags['highway'], ['abandoned', 'bus_guideway', 'construction', 'corridor', 'elevator', 'escalator', 'footway', 'motor', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps'])
        and not 'no' in tags['bicycle']
        and not 'private' in tags['service']
    ) as supports_bike,
    (
        not list_has_any(tags['highway'], ['abandoned', 'construction', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed'])
        and not 'private' in tags['service']
    ) as supports_all_nonprivate,
    (
        not list_has_any(tags['highway'], ['abandoned', 'construction', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed'])
        and not 'private' in tags['service']
    ) as supports_all_with_private,
    from ways_ord
    where
    'highway' in tags
    and 'yes' not in tags['area']
    and ST_GeometryType(geometry) == 'LINESTRING'
    and (supports_all_with_private or supports_all_nonprivate or supports_bike or supports_walk or supports_drive_service or supports_drive)
),
routes_with_refs as (
    select w.*, o.refs as node_refs, st_length_spheroid(geometry) as distance, st_length_spheroid(st_flipcoordinates(geometry)) as distance_f
    from route_ways w
    join osm o using (id)
    where o.kind = 'way'
)
select
    id,
    tags,
    unnest(case when 'yes' in tags['oneway'] then [geometry] else [geometry, st_reverse(geometry)] end) as geometry,
    lon_min, lon_max, lat_min, lat_max,
    node_refs,
    ('yes' in tags['oneway']) as oneway,
    unnest(case when 'yes' in tags['oneway'] then [node_refs[1]] else [node_refs[1], node_refs[-1]] end) as start_node,
    unnest(case when 'yes' in tags['oneway'] then [node_refs[-1]] else [node_refs[-1], node_refs[1]] end) as end_node,
    distance,
    distance_f,
    unnest(case when 'yes' in tags['oneway'] then [False] else [False, True] end) as reversed,
    supports_all_with_private, supports_all_nonprivate, supports_bike, supports_walk, supports_drive_service, supports_drive
from routes_with_refs;

-- Now add the buffered ways
ALTER TABLE ways_routes RENAME TO ways_routes_old;

create table ways_routes as
select *,
ST_Transform(
    ST_BUFFER(
        ST_Transform(geometry, 'EPSG:4326', 'EPSG:27700'), 60
    ),
    'EPSG:27700',
    'EPSG:4326'
) as buffered_geometry
from ways_routes_old;


-- creating data structure for dijkstra

-- 300 ms (~1 hr drive)
explain analyze select start_node, array_agg(struct_pack(target:=end_node, weight:=distance)) as relations
from ways_routes
where lon_min <= -0.**************** + 0.3
AND lon_max >= -0.**************** - 0.3
AND lat_min <= 51.************** + 0.3
AND lat_max >= 51.************** - 0.3
group by start_node;

-- 1.7 s to load all
explain analyze select start_node, array_agg(struct_pack(target:=end_node, weight:=distance)) as relations
from ways_routes
group by start_node;

-- just driving, 124 ms
explain analyze select start_node, array_agg(struct_pack(target:=end_node, weight:=distance)) as relations
from ways_routes
where lon_min <= -0.**************** + 0.3
AND lon_max >= -0.**************** - 0.3
AND lat_min <= 51.************** + 0.3
AND lat_max >= 51.************** - 0.3
and supports_drive
group by start_node;

--
--
--
-- scrap that! Checking OpenStreetMap ways, it turns out you need every node in the way
-- to do routing; ways don't start/stop at all junctions

-- way ID
-- start node
-- end node
-- lon_min, lon_max, lat_min, lat_max
-- geometry (linestring)
-- buffered_geometry
-- distance
-- distance_f (swap lon/lat just in case)
-- reversed

-- supports_all_with_private
-- supports_all_nonprivate
-- supports_bike
-- supports_walk
-- supports_drive_service
-- supports_drive

-------------
-- https://medium.com/@frederic.rodrigo/imposm2pgrouting-route-planning-on-openstreetmap-road-network-with-the-benefit-of-updates-1e70f280ac5e
-- Find intersections (extract all points from the ways & keep any point which appears
-- more than once, i.e. a node which is in 2 or more ways)

with pts as (
    select unnest(st_dump(st_points(geometry))).geom as point from ways_routes
)
select point
from pts
group by point
having count(*) > 1;

-- Pretty fast just for driving routes, might as well start with that
with pts as (
    select unnest(st_dump(st_points(geometry))).geom as point from ways_routes
    where supports_drive
),
intersections as (
    select point
    from pts
    group by point
    having count(*) > 1
)
select * from intersections limit 10;

-- in ways_routes, buffered geom isn't needed
-- need a better data structure which is still general but makes it
-- easier to find isochrones / run dijkstra
-- intersections for driving routes will be different than
-- intersections for walking routes
-- ideally want pre-computed edges for each travel type, with distances as weights
-- that seems like it will just need to be a separate table for each type
-- so in each of these tables, the data structure can be: edge geom, buffred edge geom, weight, root node, target node, possibly the way ID it belongs to. Still want lat/lon for each edge, maybe start/end lat/lon and a hilbert sort on the start/end lat/lon like before. Then just the edges in a certain region need to be loaded, maybe as the crow flies from the target point.
-- That won't work when rail travel is included, it'll need multiple DB reads which will be a waste, i.e. load all edges in a region then find that a train takes you far away from that region so you need to load another region, part way through the algo running, which will be slow, better to try to have everything in memory up front still, maybe that's the simplest way to stick to for now.
-- putting off that difficult step: currently read the osm pfb custom binary format made by the osm_ch algoeithm, and it has all of the contraction hierarchy information which might not be needed for calculating isocrhones anyway. Need to swap that to reading from duckdb; also what gets loaded up front? All of the buffered geometries add a lot of data which needs to be loaded at some point but the full britain data will probably be too much to load including buffered geometries. So calculate the isochrone edges and then load their buffered geometries? Adds another sequential load step which will probably be slow.

-- Using buffered ways for isochrones. This seems like a good logically correct way of doing things; the edge is inherently an edge and not an area; concave hulls are a weird approximation to make and will always be imperfect; instead by using buffered ways it's a way of saying "if I've visited this edge, then I culd have reached this area (the buffered way)". In the future, this area could be customized, e.g. for a town square, reaching any edge on the square  --- reaching all edges in the town square should lead to the union of their areas covering the whole square, not leaving "unreachable" areas in the middle which might hhappen with the basicbuffered ways approach.. Buffered ways is a nice approximation to start with. In a more correct/complete approach you'd also care about how far along each edge you make it, e.g. if an edge takes you from just below to just above the target travel time, you could count a fraction of that edge, but then what fraction of areae should you include? Maybe there's a more natural way to capture this. A slow/hard to calculate way could be to assumea "you can walk in any direction as the crow flies fom each visited point", doesn't seem right because it would ignore areas you can't access. 
-- Also, thenext step after this union of buffered ways is: as well as displaying the area for information, search for points matching criteria within that area. I wonder if there's another way to store the result of the isochrone calculation so that you can directly search for features in that resulting isochrone without having the extra step of unioning buffered ways and doing a more powerful "geometry contains" search from there. It is a nice simple approach which can work across different technologies though, it jsust needs the area gometry definition and doesn't rely on e.g. IDs of nodes lining up between different systems (which they won't).

-- there will be a query which cbreaks up ways into multiple ways based on node IDs to break at, I think postgis has it but duckdb spatial plugin doesn't yet so it's have to be a custom approach of manipulating arrays of node IDs. 

-- Also, could just stick with the osm_ch code which is working and try to make it keep the node IDs for all the nodes it includes in the isochrone



----------
-- https://www.geofabrik.de/data/routeable-vector-data.html
-- OSM preprocessed specifically for route finding: split at every junction.
-- 500 Euro for a European country
