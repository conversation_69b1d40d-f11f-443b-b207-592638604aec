-- in three.db

-- using geo-offset crate is slow, going to compare duckdb's equivalent implementation of st_buffer


select st_envelope_agg(st_buffer(geometry, 5)) from ways_ord
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
and array_contains(tags['natural'], 'wood')
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;


-- 0.001 deg ~= 100 m
-- 5663 ways
select count(*) from ways_ord
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;

-- 550 ms
select st_envelope_agg(ST_Transform(ST_BUFFER(ST_Transform(geometry, 'EPSG:4326', 'EPSG:27700'), 60), 'EPSG:27700', 'EPSG:4326')) from ways_ord
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;


-- pre-calculate buffered ways
-- table setup
create table ways_buffered as
select *, ST_Transform(
    ST_BUFFER(
        ST_Transform(geometry, 'EPSG:4326', 'EPSG:27700'), 60
    ),
    'EPSG:27700',
    'EPSG:4326'
) as buffered_geometry
from ways_ord;

-- the equivalent query
-- 43 ms
select st_envelope_agg(buffered_geometry) from ways_buffered
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;


-- The equivalent query followed by some additional buffering
-- 71 ms
-- This means it's quick to load something buffered by 60 m (or whatever the
-- pre-calculated buffer is) and extend its buffer, so any buffer >= 60 m is fast.
select ST_Transform(ST_BUFFER(ST_Transform(st_envelope_agg(buffered_geometry), 'EPSG:4326', 'EPSG:27700'), 10), 'EPSG:27700', 'EPSG:4326') from ways_buffered
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;

-- Next challenge: combining this with what's already in osm-ch
-- Maybe querying for the same node IDs is fast enough? it's redundant...

min: Coord { x: -0.9850035533654768, y: 51.43507395980596 }, max: Coord { x: -0.9519014562840944, y: 51.4688227988352 }

select st_envelope_agg(buffered_geometry)
from ways_buffered
WHERE id in [56398930, 56398931, ...]
AND lon_min <= -0.9519014562840944 + 0.05
AND lon_max >= -0.9850035533654768 - 0.05
AND lat_min <= 51.4688227988352 + 0.05
AND lat_max >= 51.43507395980596 - 0.05;


-- new Spatial feature: ST_ConcaveHull ST_Union_Agg
select ST_ConcaveHull(ST_Union_Agg(geometry)) from ways_ord
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;
