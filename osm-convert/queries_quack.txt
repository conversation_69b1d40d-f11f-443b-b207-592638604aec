CREATE TABLE osm AS
  SELECT * from st_readosm('/Users/<USER>/projects/pinpoint-proj/osm_ch/britain-and-ireland-latest.osm.pbf');

CREATE TABLE nodes AS
SELECT
    id,
    tags,
    ST_POINT(lon, lat) geometry
FROM osm
WHERE kind = 'node'
    AND tags IS NOT NULL
    AND cardinality(tags) > 0;


CREATE TABLE matching_ways AS 
SELECT id, tags
FROM osm
WHERE kind = 'way' AND tags IS NOT NULL AND cardinality(tags) > 0;

SELECT * FROM matching_ways;

-------

-- took ~10 sec

CREATE TABLE matching_ways_with_nodes_refs AS
SELECT id, UNNEST(refs) as ref, UNNEST(range(length(refs))) as ref_idx
FROM osm
SEMI JOIN matching_ways USING (id)
WHERE kind = 'way';

SELECT * FROM matching_ways_with_nodes_refs;

--------

-- took a few minutes

CREATE TABLE required_nodes_with_geometries AS
SELECT id, ST_POINT(lon, lat) geometry
FROM osm nodes
SEMI JOIN matching_ways_with_nodes_refs
ON nodes.id = matching_ways_with_nodes_refs.ref
WHERE kind = 'node';

SELECT * FROM required_nodes_with_geometries;

---------

CREATE TABLE matching_ways_linestrings AS
SELECT
    matching_ways.id,
    matching_ways.tags,
    ST_MakeLine(list(nodes.geometry ORDER BY ref_idx ASC)) linestring
FROM matching_ways
JOIN matching_ways_with_nodes_refs
ON matching_ways.id = matching_ways_with_nodes_refs.id
JOIN required_nodes_with_geometries nodes
ON matching_ways_with_nodes_refs.ref = nodes.id
GROUP BY 1, 2;

SELECT * FROM matching_ways_linestrings;

---------

-- Try again, batching into separate groups
-- 1236151087 max ID in the matching_ways table
-- Trial and error: groups of 50000000 don't get OOM

-
CREATE TABLE matching_ways_linestrings AS
SELECT
    matching_ways.id,
    matching_ways.tags,
    ST_MakeLine(list(nodes.geometry ORDER BY ref_idx ASC)) linestring
FROM matching_ways
JOIN matching_ways_with_nodes_refs
ON matching_ways.id = matching_ways_with_nodes_refs.id
JOIN required_nodes_with_geometries nodes
ON matching_ways_with_nodes_refs.ref = nodes.id
WHERE matching_ways.id < 50000000
GROUP BY 1, 2;

select max(id) from matching_ways_linestrings;

-- Now run this over and over until all IDs are done

INSERT INTO matching_ways_linestrings
SELECT
    matching_ways.id,
    matching_ways.tags,
    ST_MakeLine(list(nodes.geometry ORDER BY ref_idx ASC)) linestring
FROM matching_ways
JOIN matching_ways_with_nodes_refs
ON matching_ways.id = matching_ways_with_nodes_refs.id
JOIN required_nodes_with_geometries nodes
ON matching_ways_with_nodes_refs.ref = nodes.id
WHERE matching_ways.id > (select max(id) from matching_ways_linestrings) AND matching_ways.id < (select max(id) + 50000000 from matching_ways_linestrings)
GROUP BY 1, 2;

select * from matching_ways_linestrings;
-- 32,516,074 rows

---------

-- Converting linestrings to polygons for closed ways
-- Runs in one go

WITH way_polygon_feature AS (
    SELECT
        id,
        (
            -- if first and last nodes are the same
            ST_Equals(ST_StartPoint(linestring), ST_EndPoint(linestring))
            -- if the element doesn't have any tags leave it as a Linestring
            AND tags IS NOT NULL
            -- if the element is specifically tagged 'area':'no' -> LineString
            AND NOT (
                list_contains(map_keys(tags), 'area')
                AND list_extract(map_extract(tags, 'area'), 1) = 'no'
            )
        ) AS is_polygon
    FROM matching_ways_linestrings
)
SELECT
    matching_ways_linestrings.id,
    matching_ways_linestrings.tags,
    (CASE
        WHEN is_polygon
        THEN ST_MakePolygon(linestring)
        ELSE linestring
    END)::GEOMETRY AS geometry
FROM matching_ways_linestrings
JOIN way_polygon_feature
ON matching_ways_linestrings.id = way_polygon_feature.id;

-- More detailed definitions of closed ways:
-- Even more details could be added from here: https://wiki.openstreetmap.org/wiki/Overpass_turbo/Polygon_Features?source=post_page-----ffeb15197390--------------------------------

CREATE TABLE ways_linestrings_polygons AS
SELECT
    matching_ways_linestrings.id,
    matching_ways_linestrings.tags,
    (CASE
        WHEN (
            -- if first and last nodes are the same
            ST_Equals(ST_StartPoint(linestring), ST_EndPoint(linestring))
            -- if the element doesn't have any tags leave it as a Linestring
            AND tags IS NOT NULL
            -- For these cases, a closed way means it is a polygon
            AND (
                list_has_any(map_keys(tags), ['aeroway','amenity','boundary','building','craft','geological','historic','landuse','leisure','military','natural','office','place','shop','sport','tourism'])
                or list_has_any(tags['highway'], ['platform', 'services', 'rest_area'])
                or array_contains(tags['public_transport'], 'platform')
                or (array_contains(tags['area'], 'yes'))
            )
            -- if the element is specifically tagged 'area':'no' -> LineString
            AND NOT (
                list_contains(map_keys(tags), 'area')
                AND list_extract(map_extract(tags, 'area'), 1) = 'no'
            )
        )
        THEN ST_MakePolygon(linestring)
        ELSE linestring
    END)::GEOMETRY AS geometry
FROM matching_ways_linestrings;

select * from ways_linestrings_polygons;
-- 32,516,074 rows

------

CREATE TABLE matching_relations AS 
SELECT id, tags
FROM osm
WHERE kind = 'relation' AND len(refs) > 0
    AND tags IS NOT NULL AND cardinality(tags) > 0
    AND list_contains(map_keys(tags), 'type')
    AND list_has_any(map_extract(tags, 'type'), ['boundary', 'multipolygon']);

SELECT * FROM matching_relations;

-------

CREATE TABLE matching_relations_with_ways_refs AS
WITH unnested_relation_refs AS (
    SELECT
        r.id,
        UNNEST(refs) as ref,
        UNNEST(ref_types) as ref_type,
        UNNEST(ref_roles) as ref_role,
        UNNEST(range(length(refs))) as ref_idx
    FROM osm r
    SEMI JOIN matching_relations USING (id)
    WHERE kind = 'relation'
)
SELECT id, ref, ref_role, ref_idx
FROM unnested_relation_refs
WHERE ref_type = 'way';

SELECT * FROM matching_relations_with_ways_refs;

-----

-- construct linestrings for the ways required by the relations

CREATE TABLE required_ways_linestrings AS
WITH ways_required_by_relations_with_nodes_refs AS (
    SELECT id, UNNEST(refs) as ref, UNNEST(range(length(refs))) as ref_idx
    FROM osm ways
    SEMI JOIN matching_relations_with_ways_refs
    ON ways.id = matching_relations_with_ways_refs.ref
    WHERE kind = 'way'
),
nodes_required_by_relations_with_geometries AS (
    SELECT id, ST_POINT(lon, lat) geometry
    FROM osm nodes
    SEMI JOIN ways_required_by_relations_with_nodes_refs
    ON nodes.id = ways_required_by_relations_with_nodes_refs.ref
    WHERE kind = 'node'
)
SELECT
    ways.id,
    ST_MakeLine(list(nodes.geometry ORDER BY ref_idx ASC)) linestring
FROM ways_required_by_relations_with_nodes_refs ways
JOIN nodes_required_by_relations_with_geometries nodes
ON ways.ref = nodes.id
GROUP BY 1;

SELECT * FROM required_ways_linestrings;

------

-- After creating the required linestrings, now we can join them with relations data

CREATE TABLE matching_relations_with_ways_linestrings AS
WITH unnested_relations_with_way_linestrings AS (
    SELECT
        r.id,
        COALESCE(r.ref_role, 'outer') as ref_role,
        r.ref,
        w.linestring::GEOMETRY as geometry
    FROM matching_relations_with_ways_refs r
    JOIN required_ways_linestrings w
    ON w.id = r.ref
    ORDER BY r.id, r.ref_idx
),
any_outer_refs AS (
    -- check if any way attached to the relation has the `outer` role
    SELECT id, bool_or(ref_role == 'outer') has_any_outer_refs
    FROM unnested_relations_with_way_linestrings
    GROUP BY id
)
SELECT
    unnested_relations_with_way_linestrings.* EXCLUDE (ref_role),
    -- if none of the way refs has `outer` role - treat each ref as `outer`
    CASE WHEN any_outer_refs.has_any_outer_refs
        THEN unnested_relations_with_way_linestrings.ref_role
        ELSE 'outer'
    END as ref_role
FROM unnested_relations_with_way_linestrings
JOIN any_outer_refs
ON any_outer_refs.id = unnested_relations_with_way_linestrings.id;

SELECT * FROM matching_relations_with_ways_linestrings;

--------

CREATE TABLE matching_relations_with_merged_polygons AS
WITH merged_linestrings AS (
    SELECT
        id,
        ref_role,
        UNNEST(
            ST_Dump(ST_LineMerge(ST_Collect(list(geometry)))),
            recursive := true
        ),
    FROM matching_relations_with_ways_linestrings
    GROUP BY id, ref_role
),
relations_with_linestrings AS (
    SELECT
        id,
        ref_role,
        -- ST_Dump returns column named `geom`
        geom AS geometry,
        row_number() OVER (PARTITION BY id) as geometry_id
    FROM
        merged_linestrings
    -- discard linestrings with less than 4 points
    WHERE ST_NPoints(geom) >= 4
),
valid_relations AS (
    SELECT id, is_valid
    FROM (
        SELECT
            id,
            bool_and(
                -- Check if start point equals the end point
                ST_Equals(ST_StartPoint(geometry), ST_EndPoint(geometry))
            ) is_valid
        FROM relations_with_linestrings
        GROUP BY id
    )
    WHERE is_valid = true
)
SELECT 
    id,
    ref_role,
    ST_MakePolygon(geometry) geometry,
    geometry_id
FROM relations_with_linestrings
SEMI JOIN valid_relations
ON relations_with_linestrings.id = valid_relations.id;

SELECT * FROM matching_relations_with_merged_polygons;
-- 439,541 rows

---------

CREATE TEMP TABLE matching_relations_with_outer_polygons_with_holes AS
WITH outer_polygons AS (
    SELECT id, geometry_id, geometry
    FROM matching_relations_with_merged_polygons
    WHERE ref_role = 'outer'
), inner_polygons AS (
    SELECT id, geometry_id, geometry
    FROM matching_relations_with_merged_polygons
    WHERE ref_role = 'inner'
)
SELECT
    op.id,
    op.geometry_id,
    ST_Difference(any_value(op.geometry), ST_Union_Agg(ip.geometry)) geometry
FROM outer_polygons op
JOIN inner_polygons ip
ON op.id = ip.id AND ST_WITHIN(ip.geometry, op.geometry)
GROUP BY op.id, op.geometry_id;

CREATE TEMP TABLE matching_relations_with_outer_polygons_without_holes AS
WITH outer_polygons AS (
    SELECT id, geometry_id, geometry
    FROM matching_relations_with_merged_polygons
    WHERE ref_role = 'outer'
)
SELECT
    op.id,
    op.geometry_id,
    op.geometry
FROM outer_polygons op
ANTI JOIN matching_relations_with_outer_polygons_with_holes opwh
ON op.id = opwh.id AND op.geometry_id = opwh.geometry_id;

SELECT * FROM matching_relations_with_outer_polygons_with_holes
UNION ALL
SELECT * FROM matching_relations_with_outer_polygons_without_holes;


-- duckdb doesn't show a load bar for a long time
-- taking >10 mins
CREATE TABLE relations AS
WITH unioned_outer_geometries AS (
    SELECT id, geometry
    FROM matching_relations_with_outer_polygons_with_holes
    UNION ALL
    SELECT id, geometry
    FROM matching_relations_with_outer_polygons_without_holes
),
final_geometries AS (
    SELECT id, ST_Union_Agg(geometry) geometry
    FROM unioned_outer_geometries
    GROUP BY id
)
SELECT r_g.id, r.tags, r_g.geometry
FROM final_geometries r_g
JOIN matching_relations r
ON r.id = r_g.id;

select * from relations;
-- 217,049 rows

-------

-- Have a look at some of the data as GeoJSON
-- id 7888357 is a golf course

COPY (
select geometry from relations where id = '7888357'
) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/golf_relation.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');

select geometry from (
select geometry from relations
union
select geometry from ways_linestrings_polygons where ST_GeometryType(geometry) = 'POLYGON'
)
WHERE ST_DWithin(geometry, ST_Point(-0.9751808656307842, 51.45785273399275),0.01) AND ST_area(geometry) < 0.00001

COPY (
select geometry
from relations
WHERE ST_DWithin(geometry, ST_Point(-0.9751808656307842, 51.45785273399275),0.01) AND ST_area(geometry) < 0.00001
) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/out.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');

-- That was just relations, some e.g. buildings are still stored as ways if they're simpler

COPY (
select geometry from (
select geometry from relations
union
select geometry from ways_linestrings_polygons where ST_GeometryType(geometry) = 'POLYGON'
)
WHERE ST_DWithin(geometry, ST_Point(-0.9751808656307842, 51.45785273399275),0.001) AND ST_area(geometry) < 0.00001
) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/out.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');

-- Places with an `addr:city` to narrow it down (arbitrarily)

COPY (
select geometry from (
select tags, geometry from relations
union
select tags, geometry from ways_linestrings_polygons where ST_GeometryType(geometry) = 'POLYGON'
)
WHERE len(tags['addr:city'])>0 and ST_DWithin(geometry, ST_Point(-0.9751808656307842, 51.45785273399275),0.005) AND ST_area(geometry) < 0.00001
) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/out.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');

-- nearby railway related data

COPY (
select geometry from (
select tags, geometry from relations
union
select tags, geometry from ways_linestrings_polygons
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01) AND ST_area(geometry) < 0.1 AND len(tags['railway'])>0
) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/out.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');

-------

----- Now have tables for notable nodes, ways and relations

- nodes
- ways_linestrings_polygons
- relations
- Add bounding box information and hilbert-order the tables for spatial search

--- Next time - duckdb's spatial plugin has added its own Hilbert function ST_Hilbert
install lindel from community;
load lindel;

CREATE TABLE nodes_ord AS
select *, st_xmin(geometry) as lon_min, st_xmax(geometry) as lon_max, st_ymin(geometry) as lat_min, st_ymax(geometry) as lat_max from nodes
order by hilbert_encode([lon_min, lat_min, lon_max, lat_max]::float[4]);

create table ways_ord as
select *, st_xmin(geometry) as lon_min, st_xmax(geometry) as lon_max, st_ymin(geometry) as lat_min, st_ymax(geometry) as lat_max from ways_linestrings_polygons
order by hilbert_encode([lon_min, lat_min, lon_max, lat_max]::float[4]);

-- had to specify non-null geometry, must have been a mistake somewhere
create table relations_ord as 
  select *, st_xmin(geometry) as lon_min, st_xmax(geometry) as lon_max, st_ymin(geometry) as lat_min, st_ymax(geometry) as lat_max from relations where geometry is not null
  order by hilbert_encode([lon_min, lat_min, lon_max, lat_max]::float[4]);

---
-- Query time comparison

-- 4.28 s, 173 rows
explain analyze
select geometry from (
select tags, geometry from relations
union
select tags, geometry from ways_linestrings_polygons
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND ST_area(geometry) < 0.1 AND len(tags['railway'])>0;

-- 0.0964s, 173 rows, boom
explain analyze
select geometry from (
select * from relations_ord
union
select * from ways_ord
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND ST_area(geometry) < 0.1 AND len(tags['railway'])>0
AND lon_min <= -0.9726971352472114 + 0.01
AND lon_max >= -0.9726971352472114 - 0.01
AND lat_min <= 51.45918385677963 + 0.01
AND lat_max >= 51.45918385677963 - 0.01;

-------
duckdb 1.1.0 snuck in a bunch of spatial features
geoparquet (automatically detect geometry columns and decode them?)
rtree indexes

CREATE TABLE nodes_ord_rind AS select * from nodes_ord;
CREATE TABLE relations_ord_rind AS select * from relations_ord;
CREATE TABLE ways_ord_rind AS select * from ways_ord;

CREATE INDEX rel_sp ON relations_ord_rind USING RTREE (geometry);
CREATE INDEX node_sp ON nodes_ord_rind USING RTREE (geometry);
CREATE INDEX way_sp ON ways_ord_rind USING RTREE (geometry);

-- sucks, it's 3.26 s
-- turns out the index is not used for ST_DWithin yet: https://github.com/duckdb/duckdb_spatial/pull/383
explain analyze
select geometry from (
select tags, geometry from relations_ord_rind
union
select tags, geometry from ways_ord_rind
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND ST_area(geometry) < 0.1 AND len(tags['railway'])>0;

--

COPY (

select geometry from (
select * from relations_ord
union
select * from ways_ord
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.01)
AND ST_area(geometry) < 0.1 AND len(tags['railway'])>0
AND lon_min <= -0.9726971352472114 + 0.01
AND lon_max >= -0.9726971352472114 - 0.01
AND lat_min <= 51.45918385677963 + 0.01
AND lat_max >= 51.45918385677963 - 0.01

) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/out.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');

----

COPY (

select geometry from (
select * from relations_ord
union
select * from ways_ord
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
and array_contains(tags['natural'], 'wood')
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05

) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/out.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');

-- list available tags and values in a given area

select geometry from (
select * from relations_ord
union
select * from ways_ord
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
and array_contains(tags['natural'], 'wood')
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05
limit 5;

--
select * from nodes_ord
union
select * from relations_ord
union
select * from ways_ord

-- 51 ms
select * from ways_ord
where lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;

-- 54 ms
-- 1 hr drive roughly 0.3 deg
select * from ways_ord
where lon_min <= -0.9726971352472114 + 0.3
AND lon_max >= -0.9726971352472114 - 0.3
AND lat_min <= 51.45918385677963 + 0.3
AND lat_max >= 51.45918385677963 - 0.3;

-- Add distance

select id, tags, geometry, lon_min, lon_max, lat_min, lat_max, refs as node_refs
from ways_ord
join osm using (id)
where osm.kind = 'way';


-- Try in-memory query performance
attach 'three.db' as three;
create table three_tmp as select * from three.ways_ord;
-- 83 ms, not faster?
select * from three_tmp
where lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;

