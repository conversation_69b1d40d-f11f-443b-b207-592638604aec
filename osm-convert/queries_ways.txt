
CREATE TABLE ways AS (select * from pbf where kind == 'way');

select id, unnest(refs) from ways limit 10;

WITH way_ref as (select id, unnest(ways.refs) r from ways limit 10) select way_ref.id, way_ref.r, node.lat, node.lon from way_ref LEFT JOIN pbf node ON way_ref.r == node.id limit 10;

select * exclude(lat, lon), ST_Point(lat, lon) AS point from pbf_ord;

WITH way_ref as (select id, unnest(ways.refs) r from ways limit 10) select way_ref.id, ST_Point(node.lat, node.lon) as point from way_ref LEFT JOIN pbf_ord node ON way_ref.r == node.id limit 10;

WITH way_ref as (select id, unnest(ways.refs) r from ways limit 10) select way_ref.id, list(ST_Point(node.lat, node.lon)) as point from way_ref LEFT JOIN pbf_ord node ON way_ref.r == node.id group by way_ref.id limit 10;

; turn ways into linestrings
WITH way_ref as (select id, unnest(ways.refs) r from ways limit 100) select way_ref.id, ST_makeline(list(ST_Point(node.lat, node.lon))) as way from way_ref LEFT JOIN pbf_ord node ON way_ref.r == node.id group by way_ref.id limit 10;


WITH way_ref as (select id, unnest(ways.refs) r from ways limit 100) select way_ref.id, ST_makeline(list(ST_Point(node.lat, node.lon))) as way from way_ref LEFT JOIN pbf_ord node ON way_ref.r == node.id group by way_ref.id limit 10;
select kind, id, tags


WITH way_ref as (
    select id, tags, refs, unnest(ways.refs) r from pbf as ways where kind == 'way' limit 100
)
select way_ref.id id, ST_makeline(list(ST_Point(node.lat, node.lon))) as way
from way_ref
LEFT JOIN pbf_ord node
ON way_ref.r == node.id
group by way_ref.id
limit 10;

; select the linestrings as the linestring_2d type
WITH way_ref as (
    select id, tags, refs, unnest(ways.refs) r from pbf as ways where kind == 'way' limit 100
)
select way_ref.id id, ST_makeline(list(ST_Point(node.lat, node.lon)))::LINESTRING_2D as way
from way_ref
LEFT JOIN pbf_ord node
ON way_ref.r == node.id
group by way_ref.id
limit 10;

; OOM
CREATE TABLE ways_geo AS (
with way_geom as (
    WITH way_ref as (
        select id, unnest(ways.refs) r from pbf as ways where kind == 'way'
    )
    select way_ref.id id, list(ST_Point(node.lat, node.lon)) as way
    from way_ref
    LEFT JOIN pbf_ord node
    ON way_ref.r == node.id
    group by way_ref.id
)
select way_geom.id id, tags, refs, way_geom.way
from ways
join way_geom
on ways.id == way_geom.id
);

; create table first? Also OOM
create table way_lines as (
    WITH way_ref as (
        select id, unnest(ways.refs) r from pbf as ways where kind == 'way'
    )
    select way_ref.id id, ST_makeline(list(ST_Point(node.lat, node.lon))) as way
    from way_ref
    LEFT JOIN pbf_ord node
    ON way_ref.r == node.id
    group by way_ref.id
)

; create even more tables step by step
; this worked quickly
create table way_refs_flat as (
    select id, unnest(pbf.refs) as ref from pbf where kind == 'way'
);

; this is slow, maybe need an index? completed
create table way_lat_lon_flat as (
    select way_refs_flat.id id, node.lat lat, node.lon lon
    from way_refs_flat
    JOIN pbf_ord node
    ON way_refs_flat.ref == node.id
);

; OOM
create table ways_points as (
select id, list(ST_Point(lat, lon)) way
from way_lat_lon_flat
group by id
);


; try index?
CREATE INDEX id_idx ON way_lat_lon_flat (id);

create table ways_points as (
select id, list(lat) lats, list(lon) lons
from way_lat_lon_flat
group by id
);
; max node id
; 1255034340

;part by part?
create table ways_points as (
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id < 100000000
group by id
);

INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 100000000 and id < 200000000
group by id;

INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 200000000 and id < 300000000
group by id;

INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 300000000 and id < 400000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 400000000 and id < 500000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 500000000 and id < 600000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 600000000 and id < 700000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 700000000 and id < 800000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 800000000 and id < 900000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 900000000 and id < 1000000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 1000000000 and id < 1100000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 1100000000 and id < 1200000000
group by id;
INSERT INTO ways_points
select id, list(st_point(lat, lon)) points
from way_lat_lon_flat
where id >= 1200000000 and id < 1300000000
group by id;
; it worked

; now convert with st_makeline
create table way_lines as (
    select id, ST_makeline(points) as line
    from ways_points
)

; now combine the tags back in
create table ways_geo as (
    select way_lines.id id, tags, way_lines.line way
    from way_lines
    join ways
    on way_lines.id == ways.id
)

;;;;;;;;;;;; ways_geo has everything

select * from ways_geo where array_contains(tags['leisure'], 'swimming_area') limit 10;

; closed area?
select *, st_isclosed(way) from ways_geo where array_contains(tags['leisure'], 'swimming_area') limit 10;
; way id 98464382 is apparently not closed
select refs from ways where id == 98464382;
; The first and last ref are both 1139220787. It is closed, the ST query is wrong

select way[1] from ways_geo where id == 98464382;

; might have to manually create polygons for closed ways (last id == first id) and linestrings for others

; this isn't too bad considering it's calculating distances from all ways, but would be faster with bounding boxes.
; Takes 5.6 s
select * from ways_geo where ST_DISTANCE(way, ST_POINT(53.8, -1.2)) < 0.001 limit 10;

; find nearest rivers to a point
; only takes 0.073 s !
select *, ST_DISTANCE(way, ST_POINT(53.8, -1.2)) dist from ways_geo where dist < 1 AND array_contains(tags['waterway'], 'river') LIMIT 5;

; takes 0.068 s
select * from ways_geo where ST_DISTANCE(way, ST_POINT(53.8, -1.2)) < 1 AND array_contains(tags['waterway'], 'river') LIMIT 5;


; Same query using DWithin takes 0.048 s
select * from ways_geo where ST_DWithin(way, ST_POINT(53.8, -1.2), 1) AND array_contains(tags['waterway'], 'river') LIMIT 5;

; 0.76 sec to find ALL in range and select the nearest 5
select *, ST_DISTANCE(way, ST_POINT(53.8, -1.2)) dist from ways_geo where dist < 1 AND array_contains(tags['waterway'], 'river') ORDER BY dist ASC LIMIT 5;


; 10x distance range takes 0.9 s (same results)
select *, ST_DISTANCE(way, ST_POINT(53.8, -1.2)) dist from ways_geo where dist < 10 AND array_contains(tags['waterway'], 'river') ORDER BY dist ASC LIMIT 5;

; boats
select * from ways_geo where ST_DWithin(way, ST_POINT(53.8, -1.2), 0.1) AND array_contains(tags['boat'], 'yes') LIMIT 5;

;;;;;;;; bounding box
select *, st_extent(way) bbox from ways_geo limit 5;

create table ways_geo as (
    select way_lines.id id, tags, way_lines.line way, st_extent(way_lines.line) bbox
    from way_lines
    join ways
    on way_lines.id == ways.id
);

;;;; find points near a river within a bounding box
create table points_geo as (select *, st_point(lat, lon) point from pbf_ord);

select * from ways_geo where ST_DWithin(way, ST_POINT(53.8, -1.2), 1) AND array_contains(tags['waterway'], 'river') LIMIT 5;

select * from points_geo join (select * from ways_geo where array_contains(tags['waterway'], 'river')) ways on ST_DWithin(ways.way, points_geo.point, 1) limit 10;
; not much faster
select * from points_geo join (select * from ways_geo where array_contains(tags['waterway'], 'river')) ways on st_intersects(ways.bbox, points_geo.point) and  ST_DWithin(ways.way, points_geo.point, 1) limit 10;

select * from points_geo join (select * from ways_geo WHERE ST_DWithin(way, ST_POINT(53.8, -1.2), 1) AND array_contains(tags['waterway'], 'river')) ways on st_intersects(ways.bbox, points_geo.point) and  ST_DWithin(ways.way, points_geo.point, 1) limit 10;

select * from points_geo join (select * from ways_geo WHERE st_intersects(bbox, ST_POINT(53.8, -1.2)) AND ST_DWithin(way, ST_POINT(53.8, -1.2), 1) AND array_contains(tags['waterway'], 'river')) ways on st_intersects(ways.bbox, points_geo.point) and  ST_DWithin(ways.way, points_geo.point, 1) limit 10;

select *
from points_geo
join ways_geo
on ST_DWithin(ways_geo.bbox, points_geo.point, 1)
WHERE ST_DWithin(way, ST_POINT(53.8, -1.2), 1) AND ST_DWithin(point, ST_POINT(53.8, -1.2), 1) AND array_contains(ways_geo.tags['waterway'], 'river') and ST_DWithin(way, points_geo.point, 1) limit 10;

; getting closer, this takes 1.43 s
select *
from points_geo
join ways_geo
on ST_intersects_extent(ways_geo.bbox, st_buffer(points_geo.point, 1))
WHERE st_intersects_extent(ways_geo.bbox, st_buffer(ST_POINT(53.8, -1.2), 1))
and st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 1))
and ST_DWithin(way, ST_POINT(53.8, -1.2), 1)
AND ST_DWithin(point, ST_POINT(53.8, -1.2), 1)
AND array_contains(ways_geo.tags['waterway'], 'river')
and ST_DWithin(way, points_geo.point, 1)
limit 10;

; takes 1.34 s
select *
from points_geo
join ways_geo
on ST_DWithin(ways_geo.way, points_geo.point, 1)
WHERE st_intersects_extent(ways_geo.bbox, st_buffer(ST_POINT(53.8, -1.2), 1))
and st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 1))
AND array_contains(ways_geo.tags['waterway'], 'river')
limit 10;

; takes 1.24 s
select *
from (select * from points_geo where st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 1))) points_geo
join (select * from ways_geo where st_intersects_extent(ways_geo.bbox, st_buffer(ST_POINT(53.8, -1.2), 1)) and array_contains(ways_geo.tags['waterway'], 'river')) ways_geo
on ST_DWithin(ways_geo.way, points_geo.point, 1)
limit 10;


;;;; try using the duckdb-specific types point2d and linestring_2d
create table points_g as (select * exclude(point), st_point2d(lat, lon) as point from points_geo);

; Getting errors when trying to cast to linestring_2d
select line::linestring_2d from way_lines limit 1;



;;;; create ways with linestring_2d type

create table ways_point2d as (
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id < 100000000
group by id
);

INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 100000000 and id < 200000000
group by id;

;;;;;;; .
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 200000000 and id < 300000000
group by id;

INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 300000000 and id < 400000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 400000000 and id < 500000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 500000000 and id < 600000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 600000000 and id < 700000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 700000000 and id < 800000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 800000000 and id < 900000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 900000000 and id < 1000000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 1000000000 and id < 1100000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 1100000000 and id < 1200000000
group by id;
INSERT INTO ways_point2d
select id, list(struct_pack(x:= lat::DECIMAL(10, 7), y:= lon::DECIMAL(10, 7)))::LINESTRING_2D line
from way_lat_lon_flat
where id >= 1200000000 and id < 1300000000
group by id;

; now combine the tags back in
create table ways_g as (
    select ways_point2d.id id, tags, ways_point2d.line way
    from ways_point2d
    join ways
    on ways_point2d.id == ways.id
);

;;; Now see if the same query is any faster.
;; Nope = 1.48 s
select *
from (select * from points_g where st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 1))) p
join (select * from ways_g where st_intersects_extent(way, st_buffer(ST_POINT(53.8, -1.2), 1)) and array_contains(ways_g.tags['waterway'], 'river')) w
on ST_DWithin(w.way, p.point, 1)
limit 10;

; 1.47 s
select *
from points_g
join ways_g
on ST_DWithin(way, point, 1)
WHERE st_intersects_extent(way, st_buffer(ST_POINT(53.8, -1.2), 1))
and st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 1))
AND array_contains(ways_g.tags['waterway'], 'river')
limit 10;

select *
from points_g
join ways_g
on ST_DWithin(way, point, 0.05)
WHERE st_intersects_extent(way, st_buffer(ST_POINT(53.8, -1.2), 0.05))
and st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 0.3))
AND array_contains(ways_g.tags['waterway'], 'river');

select *
from points_g
join ways_g
on ST_DWithin(way, point, 0.05)
WHERE st_intersects_extent(way, st_buffer(ST_POINT(53.8, -1.2), 0.05))
and st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 0.05))
AND array_contains(ways_g.tags['waterway'], 'river');

;; A multiline string of all ways meeting the way criteria - 0.893 s
select st_union_agg(way) multiway
    from ways_g
    where st_intersects_extent(way, st_buffer(ST_POINT(53.8, -1.2), 0.05))
    and array_contains(ways_g.tags['waterway'], 'river')

with x as (
    select st_union_agg(way) multiway
    from ways_g
    where st_intersects_extent(way, st_buffer(ST_POINT(53.8, -1.2), 0.05))
    and array_contains(ways_g.tags['waterway'], 'river')
)
select *
from points_g
join x
on ST_DWithin(x.multiway, point, 0.05)
where st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 0.3));


;;;; order the ways by centroid
install lindel from community;
load lindel;

CREATE TABLE ways_g_ord AS
  (
    SELECT * from ways_g
    order by hilbert_encode([st_x(st_centroid(way)), st_y(st_centroid(way))]::double[2])
  );

;;; Try those queries again
; was 1.47 s
; still 1.33 s
select *
from points_g
join ways_g_ord
on ST_DWithin(way, point, 1)
WHERE st_intersects_extent(way, st_buffer(ST_POINT(53.8, -1.2), 0.1))
and st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 0.1))
AND array_contains(ways_g_ord.tags['waterway'], 'river')
limit 10;


select *
from points_g
where ST_DWithin(point, st_buffer(ST_POINT(53.8, -1.2), 0.1))
limit 100;

; 0.020 s
SELECT * FROM points_g
WHERE array_contains(tags['man_made'], 'telephone_box')
AND lat BETWEEN 53.8 AND 54 AND lon BETWEEN -1.2 AND -1;

; 1.63 s
SELECT * FROM points_g
WHERE array_contains(tags['man_made'], 'telephone_box')
AND st_intersects(point, {'min_x': 53.8, 'min_y': -1.2, 'max_x': 54.0, 'max_y': -1.0}::BOX_2D);

; 1.54 s - swapping the st_intersects_extent arg order also makes no difference
SELECT * FROM points_g
WHERE array_contains(tags['man_made'], 'telephone_box')
AND st_intersects_extent(point, {'min_x': 53.8, 'min_y': -1.2, 'max_x': 54.0, 'max_y': -1.0}::BOX_2D);


with bbox as (select {'min_x': 53.8, 'min_y': -1.2, 'max_x': 54.0, 'max_y': -1.0}::BOX_2D)
SELECT * FROM points_g
WHERE array_contains(tags['man_made'], 'telephone_box')
AND point.x::double BETWEEN 53.8 AND 54
AND st_y(point) BETWEEN -1.2 AND -1;

;; try it the hard way to see how quick it could be
select *,
st_xmin(st_extent(way)) as xmin,
st_ymin(st_extent(way)) as ymin,
st_xmax(st_extent(way)) as xmax,
st_ymax(st_extent(way)) as ymax,
from ways_g_ord limit 10;

CREATE TABLE ways_gos AS (
    select *,
    st_xmin(st_extent(way)) as xmin,
    st_ymin(st_extent(way)) as ymin,
    st_xmax(st_extent(way)) as xmax,
    st_ymax(st_extent(way)) as ymax,
    from ways_g_ord
    order by hilbert_encode([xmin, ymin, xmax, ymax]::float[4])
);


; was 1.4 s
; this is 0.027 s
select *
from points_g
join ways_gos
on ST_DWithin(way, point, 1)
WHERE st_intersects_extent(way, st_buffer(ST_POINT(53.8, -1.2), 0.1))
and st_intersects_extent(point, st_buffer(ST_POINT(53.8, -1.2), 0.1))
AND array_contains(ways_gos.tags['waterway'], 'river')
and xmin < 53.9 and xmax > 53.7 and ymin < -1.1 and ymax > -1.3
and lat between 53.7 and 53.9 and lon between -1.3 and -1.1
limit 10;

; no need for the slow st_intersects_extent
; 0.026 s
select *
from points_g
join ways_gos
on ST_DWithin(way, point, 1)
WHERE array_contains(ways_gos.tags['waterway'], 'river')
and xmin < 53.9 and xmax > 53.7 and ymin < -1.1 and ymax > -1.3
and lat between 53.7 and 53.9 and lon between -1.3 and -1.1
limit 10;

select distinct points_g.tags
from points_g
join ways_gos
on ST_DWithin(way, point, 1)
WHERE array_contains(ways_gos.tags['waterway'], 'river')
and xmin < 54 and xmax > 53 and ymin < -1 and ymax > -2
and lat between 53 and 54 and lon between -2 and -1
and cardinality(points_g.tags) != 0
limit 10;


with x as (
    select st_union_agg(way) multiway
    from ways_gos
    where array_contains(ways_gos.tags['waterway'], 'river')
    and xmin < 53.2 and xmax > 53 and ymin < 0.2 and ymax > 0
)
select distinct points_g.tags, lat, lon
from points_g
join x
on ST_DWithin(x.multiway, point, 0.001)
where lat between 53 and 53.2 and lon between 0 and 0.2;

select distinct points_g.tags
from points_g
join ways_gos
on ST_DWithin(way, point, 1)
WHERE array_contains(ways_gos.tags['waterway'], 'river')
and xmin < 53.1 and xmax > 53 and ymin < 0.1 and ymax > 0
and lat between 53 and 53.1 and lon between 0 and 0.1;

; in reading and near water
; 51.46,-0.97
; 0.12 s
select distinct points_g.tags
from points_g
join ways_gos
on ST_DWithin(way, point, 0.001)
WHERE array_contains(ways_gos.tags['waterway'], 'river')
and xmin < (51.46+0.01) and xmax > (51.46-0.01) and ymin < (-0.97+0.01) and ymax > (-0.97-0.01)
and lat between (51.46-0.01) and (51.46+0.01) and lon between (-0.97-0.01) and (-0.97+0.01);


; export to geojson for visualizing
COPY (
select distinct st_point(lon, lat)
from points_g
join ways_gos
on ST_DWithin(way, point, 0.001)
WHERE array_contains(ways_gos.tags['waterway'], 'river')
and xmin < (51.46+0.05) and xmax > (51.46-0.05) and ymin < (-0.97+0.05) and ymax > (-0.97-0.05)
and lat between (51.46-0.05) and (51.46+0.05) and lon between (-0.97-0.05) and (-0.97+0.05)
) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/out.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');


COPY (
select distinct st_point(lon, lat)
from points_g
join ways_gos
on ST_DWithin(way, point, 0.001)
WHERE array_contains(ways_gos.tags['waterway'], 'river')
and xmin < (51.46+0.05) and xmax > (51.46-0.05) and ymin < (-0.97+0.05) and ymax > (-0.97-0.05)
and lat between (51.46-0.05) and (51.46+0.05) and lon between (-0.97-0.05) and (-0.97+0.05)
) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/out.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');


COPY (
select distinct st_point(lon, lat)
from points_g
join ways_gos
on ST_DWithin(way, point, 0.001)
WHERE array_contains(ways_gos.tags['building'], 'school')
and cardinality(points_g.tags) != 0
and xmin < (51.46+0.05) and xmax > (51.46-0.05) and ymin < (-0.97+0.05) and ymax > (-0.97-0.05)
and lat between (51.46-0.05) and (51.46+0.05) and lon between (-0.97-0.05) and (-0.97+0.05)
) TO '/Users/<USER>/projects/pinpoint-proj/osm-convert/school.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');



; close to a school
select *
from points_g p1
join points_g p2
on ST_DWithin(p2.point, p1.point, 0.001)
limit 10;
WHERE length(p2.tags['school']) > 0
and p1.lat between (51.46-0.05) and (51.46+0.05) and p1.lon between (-0.97-0.05) and (-0.97+0.05)
and p2.lat between (51.46-0.05) and (51.46+0.05) and p2.lon between (-0.97-0.05) and (-0.97+0.05);



with x as (
    select st_union_agg(point) points
    from points_g
    where length(tags['school']) > 0
    and lat between (51.46-0.05) and (51.46+0.05) and lon between (-0.97-0.05) and (-0.97+0.05)
)
select distinct points_g.tags, lat, lon
from points_g
join x
on ST_DWithin(x.multiway, point, 0.001)
where lat between 53 and 53.2 and lon between 0 and 0.2;
