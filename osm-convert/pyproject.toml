[project]
name = "osm-convert"
version = "0.1.0"
description = "Convert OSM PBF files to DuckDB with spatial types"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "duckdb>=1.2.2",
]

[project.scripts]
osm-convert = "osm_convert.cli:main"

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]
include = ["osm_convert*"]
