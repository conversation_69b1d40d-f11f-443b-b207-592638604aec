[project]
name = "osm-query"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "duckdb>=1.1.2",
    "ipykernel>=6.29.5",
    "ipywidgets-bokeh>=1.6.0",
    "lonboard>=0.10.2",
    "panel>=1.5.2",
    "polars>=1.9.0",
    "pyarrow>=17.0.0",
    "shapely>=2.0.6",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
