import duckdb
import panel as pn
import param
from lonboard import Map, PathLayer, PolygonLayer
from lonboard._viewport import compute_view
from lonboard.colormap import apply_continuous_cmap

pn.extension("ipywidgets")

con = duckdb.connect(
    "/Users/<USER>/projects/pinpoint-proj/osm-convert/three.db", read_only=True
)
con.install_extension("spatial")
con.load_extension("spatial")


def driving_ways() -> PathLayer:
    sql = """
    select * from ways_ord
    where lon_min <= -0.9726971352472114 + 0.05
    AND lon_max >= -0.9726971352472114 - 0.05
    AND lat_min <= 51.45918385677963 + 0.05
    AND lat_max >= 51.45918385677963 - 0.05
    
    and 'highway' in tags
    and 'yes' not in tags['area']
    and not list_has_any(tags['highway'], ['abandoned', 'bridleway', 'bus_guideway', 'construction', 'corridor', 'cycleway', 'elevator', 'escalator', 'footway', 'no', 'path', 'pedestrian', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'service', 'steps', 'track'])
    and not 'no' in tags['motor_vehicle']
    and not 'no' in tags['motorcar']
    and not list_has_any(tags['service'], ['alley', 'driveway', 'emergency_access', 'parking', 'parking_aisle', 'private']);
    """
    l = PathLayer.from_duckdb(sql, con)
    l.opacity = 0.8
    l.get_color = [0, 0, 200]
    # l.get_fill_color = [0, 0, 0]
    return l


def walking_ways() -> PathLayer:
    sql = """
    select * from ways_ord
    where lon_min <= -0.9726971352472114 + 0.05
    AND lon_max >= -0.9726971352472114 - 0.05
    AND lat_min <= 51.45918385677963 + 0.05
    AND lat_max >= 51.45918385677963 - 0.05

    and 'highway' in tags
    and 'yes' not in tags['area']
    and not list_has_any(tags['highway'], ['abandoned', 'bus_guideway', 'construction', 'corridor', 'elevator', 'escalator', 'footway', 'motor', 'no', 'planned', 'platform', 'proposed', 'raceway', 'razed', 'steps'])
    and not 'no' in tags['bicycle']
    and not 'private' in tags['service']
    and ST_GeometryType(geometry) == 'LINESTRING';
    """
    l = PathLayer.from_duckdb(sql, con)
    l.opacity = 0.8
    l.get_color = [0, 0, 200]
    # l.get_fill_color = [0, 0, 0]
    return l


def get_polydata(tag: str = "natural", value: str | None = None) -> PolygonLayer:
    # EPSG:4326 is the standard lat/lon coordinate system
    # EPSG:27700 is the british national grid coordinate system https://spatialreference.org/ref/epsg/27700/
    sql = """
    select ST_Transform(ST_BUFFER(ST_Transform(geometry, 'EPSG:4326', 'EPSG:27700'), 10), 'EPSG:27700', 'EPSG:4326') as geometry from (
    select * from relations_ord
    union
    select * from ways_ord
    )
    WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
    AND lon_min <= -0.9726971352472114 + 0.05
    AND lon_max >= -0.9726971352472114 - 0.05
    AND lat_min <= 51.45918385677963 + 0.05
    AND lat_max >= 51.45918385677963 - 0.05
    """
    if value is not None:
        sql += f" and array_contains(tags['{tag}'], '{value}');"
    else:
        sql += f" and len(tags['{tag}']) > 0;"
    # query = con.sql(sql)
    # print(query)
    l = PolygonLayer.from_duckdb(sql, con)
    l.opacity = 0.8
    l.get_fill_color = [0, 0, 0]
    return l


def get_tags():
    query = """
    with ways_relations as (
        select * from relations_ord
        union
        select * from ways_ord
    ),
    tags_in_bounds as (
        select unnest(flatten(array_agg(map_entries(tags))), recursive := true) from ways_relations
        WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
        AND lon_min <= -0.9726971352472114 + 0.05
        AND lon_max >= -0.9726971352472114 - 0.05
        AND lat_min <= 51.45918385677963 + 0.05
        AND lat_max >= 51.45918385677963 - 0.05
        group by 1=1
    ),
    grouped_tags as (
        select key, list_distinct(array_agg(value)) as value from tags_in_bounds
        group by key
    )
    select map_from_entries(array_agg(struct_pack(k:= key, v:= value))) as tags
    from grouped_tags
    group by 1=1;
"""

    x = con.query(query)
    result = {
        k: v
        for k, v in sorted(
            {el["key"]: el["value"] for el in x.pl().to_dicts()[0]["tags"]}.items(),
            key=lambda x: x[0],
        )
    }
    return result


tags = get_tags()


class StateViewer(pn.viewable.Viewer):
    value: Map = param.ClassSelector(class_=Map, doc="The map object", constant=True)

    tag: str = param.Selector(default="natural", objects=tags.keys())
    tag_value: str | None = param.Selector(default="wood", objects=tags["natural"])
    # tag_value_select = (
    #     pn.widgets.Select.from_param(tag_value, sizing_mode="stretch_width"),
    # )
    selection_layer: PolygonLayer = param.Parameter(default=get_polydata())

    def __init__(self, **params):
        params["value"] = params.get(
            "value",
            Map(
                layers=[],
                view_state={
                    "longitude": -0.9726971352472114,
                    "latitude": 51.45918385677963,
                    "zoom": 11,
                },
            ),
        )

        super().__init__(**params)

        self.value.layout.width = self.value.layout.height = "100%"
        self.value._height = 1000
        self.settings = pn.Column(
            pn.widgets.Select.from_param(self.param.tag, sizing_mode="stretch_width"),
            # self.tag_value_select,
            pn.widgets.Select.from_param(
                self.param.tag_value, sizing_mode="stretch_width"
            ),
            margin=5,
            sizing_mode="fixed",
            width=300,
        )
        self.view = pn.Column(
            self._title, pn.pane.IPyWidget(self.value, sizing_mode="stretch_both")
        )
        self._layout = pn.Row(
            pn.Column(self.settings, sizing_mode="fixed", width=300),
            self.view,
            sizing_mode="stretch_both",
        )

    def __panel__(self):
        return self._layout

    @param.depends("tag", watch=True, on_init=True)
    def _update_tag(self):
        self.param.tag_value.objects = [None, *tags[self.tag]]
        self.tag_value = None
        self.selection_layer = get_polydata(tag=self.tag, value=None)

    @param.depends("tag_value", watch=True, on_init=True)
    def _update_tag_value(self):
        self.selection_layer = get_polydata(tag=self.tag, value=self.tag_value)

    def _get_color(self):
        palette = to_palette(self.cmap)
        normalized_scale_rank = (self.selection_layer["scalerank"] - 3) / 9
        return apply_continuous_cmap(normalized_scale_rank, palette, alpha=self.alpha)

    @param.depends("selection_layer", watch=True)
    def _update_value(self):
        self.value.layers = [self.selection_layer, walking_ways()]
        # self._fly_to_center()

    def _fly_to_center(self):
        computed_view_state = compute_view(self.value.layers)
        self.value.fly_to(
            **computed_view_state,
            duration=1000,
        )

    def _title(self):
        return "# Title"


# m = Map([])

# # Fit to the available space

# m.layout.height = "100%"
# m.layout.width = "100%"
# m._height = 1000

# m.layers = [get_polydata()]

# pn.Column(
#     pn.pane.IPyWidget(m, height=1000, width=1600),
# ).servable()

viewer = StateViewer()
pn.template.FastListTemplate(
    title="Works with Lonboard",
    sidebar=[viewer.settings],
    main=[viewer.view],
    main_layout=None,
).servable()

"""
panel serve osm_query/page.py --autoreload
"""
