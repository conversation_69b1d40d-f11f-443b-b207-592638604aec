from pathlib import Path

import duckdb
from lonboard import Map, PolygonLayer

con = duckdb.connect(
    "/Users/<USER>/projects/pinpoint-proj/osm-convert/three.db", read_only=True
)
con.install_extension("spatial")
con.load_extension("spatial")

sql = """
select geometry from (
select * from relations_ord
union
select * from ways_ord
)
WHERE ST_DWithin(geometry, ST_Point(-0.9726971352472114, 51.45918385677963),0.05)
and array_contains(tags['natural'], 'wood')
AND lon_min <= -0.9726971352472114 + 0.05
AND lon_max >= -0.9726971352472114 - 0.05
AND lat_min <= 51.45918385677963 + 0.05
AND lat_max >= 51.45918385677963 - 0.05;
"""
# query = con.sql(sql)
# print(query)
l = PolygonLayer.from_duckdb(sql, con)
m = Map(l)

Path("f.html").write_text(m.to_html())
