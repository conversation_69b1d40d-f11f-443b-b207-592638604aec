# Overall notes

## OpenStreetMap download of UK&Ireland

https://download.geofabrik.de/europe/britain-and-ireland.html



### Log

#### 27 Feb

Found osm_ch rust - open street map contraction hierarchies
Downloaded OSM data from https://download.geofabrik.de/europe/britain-and-ireland.html
Ran the pre-processing step which calculates contraction hierarchies - took ~50 mins on the macbook air and hit ~25 GB RAM (using swap) so better to run it on the PC

TRAVEL_TYPE is set to Car. Will have to run the whole thing again for each different travel type

#### 28 Feb

Reprocessed as pedestrian travel type
Found that the logic for working out which ways can be walked on by pedestrians was incomplete, e.g. didn't include the footpaths across Christchurch Meadow.
Updated foot-friendly way logic, it's all based on OSM tags.
Some roads aren't tagged very well.

#### 2 Apr

PostGIS - use docker, use pgadmin as a web gui
https://www.youtube.com/watch?v=h-Cr5Ana0GA&t=75s

A talk about a wayfinding app using speech/audio rather than a map on a screen. Includes e.g. data sources they used to work out which roads are important and from which locations you can see landmarks - Flickr geo tagged photos, review/checkin websites

#### 26 Nov

Using duckdb with its spatial plugin. They updated it a few months ago with a load of new spatial features.

Converted open streetmap data to duckdb geometry types in `osm-convert`
Getting fast query execution by Hilbert ordering by lat/lon and querying with bounding boxes to use the zone map query optimizations

Switched to using just England OSM data in `osm_ch` so that it's faster to start up and easily runs on MacBook Air (previously went out of memory)

Calculating just car travel times in `osm_ch` (without rail data yet). The idea for integrating the connection scan algorithm (csa-rust) is simple but haven't tried it yet. Start the connection scan each time the isochrone scan reaches a train station node using an arbitrarily chosen time of day for now; later might want to average multiple times through the week and store the result. Then add the destination station nodes back in the isochrone scan and everything should work.

None of the contraction hierarchy data info is actually being used atm, and it's not clear whether it could be useful for calculating isochrones - tbd, but basic dijkstra isochrones are fast enough for now (~10s of ms to cover Reading)

Tried using the rust crate `geo-offset` for dilating/offsetting/buffering geometries (everywhere uses a different word for this). Had to transform coordinates to a UK-centric coordinate system otherwise it was making weird simple shapes. Visually looks good but really slow, next idea: pre-calculate dilated versions of every way, then use duckdb to load & envelope aggregate the ways which are part of the path. (Could also try seeing how fast it is to just envelope agg some ways first which will be simpler if it's fast enough...)

Started using deckgl in `osm-query` - nice fast rendering, nice styles out of the box
Looked at `tilemaker` which lets your create styled vector maps based on any open streetmap data, with different features at different zooms. These tiles should work with deckgl, so I can host a tile server if needed.


