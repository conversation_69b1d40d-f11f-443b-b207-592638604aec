"""
OSM Processor - Convert OSM PBF files to DuckDB with spatial types

This script processes OpenStreetMap PBF files and converts them into DuckDB
tables with proper spatial types (points, linestrings, polygons) for efficient
spatial queries.
"""

import logging
from pathlib import Path

import duckdb

"""
TODO
- Use temp tables instead of tables, or at least drop tables when done
- Hilbert order data sooner to see if it's faster?
- Speed up some of the joins, there's probably a lot of room for improvement
"""

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("osm_processor")


class OSMProcessor:
    """Process OSM PBF files into DuckDB with spatial types"""

    def __init__(
        self,
        pbf_path: str,
        db_path: str,
        batch_size: int = 50000000,
        threads: int | None = None,
    ):
        """
        Initialize the OSM processor

        Args:
            pbf_path: Path to the OSM PBF file
            db_path: Path to the output DuckDB database
            batch_size: Batch size for processing ways (to avoid OOM errors)
            threads: Number of threads for DuckDB to use (None = use default)
        """
        self.pbf_path = Path(pbf_path).resolve()
        self.db_path = Path(db_path).resolve()
        self.batch_size = batch_size
        self.threads = threads
        self.conn = None

        if not self.pbf_path.exists():
            raise FileNotFoundError(f"OSM PBF file not found: {self.pbf_path}")

        logger.info(f"Processing {self.pbf_path} to {self.db_path}")

    def connect(self):
        """Connect to the DuckDB database and initialize spatial extension"""
        logger.info(f"Connecting to DuckDB database: {self.db_path}")
        self.conn = duckdb.connect(str(self.db_path))

        # Set number of threads if specified
        if self.threads is not None:
            logger.info(f"Setting DuckDB to use {self.threads} threads")
            self.conn.execute(f"PRAGMA threads={self.threads};")

        # Load spatial extension
        self.conn.execute("INSTALL spatial;")
        self.conn.execute("LOAD spatial;")

        logger.info("DuckDB connection established with spatial extension loaded")

    def close(self):
        """Close the DuckDB connection"""
        if self.conn:
            self.conn.close()
            logger.info("DuckDB connection closed")

    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self

    def __exit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: object | None,
    ) -> None:
        """Context manager exit"""
        self.close()

    def process(self):
        """Process the OSM PBF file into DuckDB tables"""
        logger.info("Starting OSM processing pipeline")

        # Step 1: Load OSM data
        self._load_osm_data()

        # Step 2: Process nodes
        self._process_nodes()

        # Step 3: Process ways
        self._process_ways()

        # Step 4: Process relations
        self._process_relations()

        # Step 5: Optimize for spatial queries
        self._optimize_for_spatial_queries()

        logger.info("OSM processing pipeline completed successfully")

    def _table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database"""
        result = self.conn.execute(f"""
            SELECT COUNT(*) FROM information_schema.tables
            WHERE table_name = '{table_name}'
        """).fetchone()[0]
        return result > 0

    def _load_osm_data(self):
        """Load OSM data from PBF file into DuckDB"""
        if self._table_exists("osm"):
            logger.info("Table 'osm' already exists, skipping loading OSM data")
            return

        logger.info("Loading OSM data from PBF file")

        self.conn.execute(f"""
            CREATE TABLE osm AS
            SELECT * FROM st_readosm('{self.pbf_path}');
        """)

        # Get some stats
        count = self.conn.execute("SELECT COUNT(*) FROM osm").fetchone()[0]
        logger.info(f"Loaded {count} OSM elements")

    def _process_nodes(self):
        """Process nodes and convert to point geometries"""
        if self._table_exists("nodes"):
            logger.info("Table 'nodes' already exists, skipping node processing")
            return

        logger.info("Processing nodes")

        self.conn.execute("""
            CREATE TABLE nodes AS
            SELECT
                id,
                tags,
                ST_POINT(lon, lat) geometry
            FROM osm
            WHERE kind = 'node'
                AND tags IS NOT NULL
                AND cardinality(tags) > 0;
        """)

        count = self.conn.execute("SELECT COUNT(*) FROM nodes").fetchone()[0]
        logger.info(f"Processed {count} nodes with tags")

    def _process_ways(self):
        """Process ways and convert to linestrings/polygons"""
        if self._table_exists("ways_linestrings_polygons"):
            logger.info(
                "Table 'ways_linestrings_polygons' already exists, skipping way processing"
            )
            return

        logger.info("Processing ways")

        # Step 1: Extract ways with tags
        if not self._table_exists("matching_ways"):
            self.conn.execute("""
                CREATE TABLE matching_ways AS
                SELECT id, tags
                FROM osm
                WHERE kind = 'way' AND tags IS NOT NULL AND cardinality(tags) > 0;
            """)

        # Step 2: Extract node references for ways
        if not self._table_exists("matching_ways_with_nodes_refs"):
            self.conn.execute("""
                CREATE TABLE matching_ways_with_nodes_refs AS
                SELECT id, UNNEST(refs) as ref, UNNEST(range(length(refs))) as ref_idx
                FROM osm
                SEMI JOIN matching_ways USING (id)
                WHERE kind = 'way';
            """)

        # Step 3: Get geometries for required nodes
        if not self._table_exists("required_nodes_with_geometries"):
            self.conn.execute("""
                CREATE TABLE required_nodes_with_geometries AS
                SELECT id, ST_POINT(lon, lat) geometry
                FROM osm nodes
                SEMI JOIN matching_ways_with_nodes_refs
                ON nodes.id = matching_ways_with_nodes_refs.ref
                WHERE kind = 'node';
            """)

        # Step 4: Create linestrings for ways (in batches to avoid OOM)
        # TODO the OOM might be avoidable by reducing the number of threads being used
        if not self._table_exists("matching_ways_linestrings"):
            logger.info("Creating linestrings for ways (in batches)")

            # Get max way ID
            max_id = self.conn.execute("SELECT MAX(id) FROM matching_ways").fetchone()[
                0
            ]

            # Create initial batch
            self.conn.execute(f"""
                CREATE TABLE matching_ways_linestrings AS
                SELECT
                    matching_ways.id,
                    matching_ways.tags,
                    ST_MakeLine(list(nodes.geometry ORDER BY ref_idx ASC)) linestring
                FROM matching_ways
                JOIN matching_ways_with_nodes_refs
                ON matching_ways.id = matching_ways_with_nodes_refs.id
                JOIN required_nodes_with_geometries nodes
                ON matching_ways_with_nodes_refs.ref = nodes.id
                WHERE matching_ways.id < {self.batch_size}
                GROUP BY 1, 2;
            """)

            # Process remaining batches
            current_max = self.batch_size
            while current_max < max_id:
                logger.info(
                    f"Processing ways with IDs {current_max} to {current_max + self.batch_size}"
                )

                self.conn.execute(f"""
                    INSERT INTO matching_ways_linestrings
                    SELECT
                        matching_ways.id,
                        matching_ways.tags,
                        ST_MakeLine(list(nodes.geometry ORDER BY ref_idx ASC)) linestring
                    FROM matching_ways
                    JOIN matching_ways_with_nodes_refs
                    ON matching_ways.id = matching_ways_with_nodes_refs.id
                    JOIN required_nodes_with_geometries nodes
                    ON matching_ways_with_nodes_refs.ref = nodes.id
                    WHERE matching_ways.id >= {current_max} AND matching_ways.id < {current_max + self.batch_size}
                    GROUP BY 1, 2;
                """)

                current_max += self.batch_size

        # Step 5: Convert closed ways to polygons based on OSM tagging conventions
        logger.info("Converting closed ways to polygons")

        self.conn.execute("""
            CREATE TABLE ways_linestrings_polygons AS
            SELECT
                matching_ways_linestrings.id,
                matching_ways_linestrings.tags,
                (CASE
                    WHEN (
                        -- if first and last nodes are the same
                        ST_Equals(ST_StartPoint(linestring), ST_EndPoint(linestring))
                        -- if the element doesn't have any tags leave it as a Linestring
                        AND tags IS NOT NULL
                        -- For these cases, a closed way means it is a polygon
                        AND (
                            list_has_any(map_keys(tags)::VARCHAR[], ['aeroway','amenity','boundary','building','craft','geological','historic','landuse','leisure','military','natural','office','place','shop','sport','tourism']::VARCHAR[])
                            OR (
                                tags['highway'] IS NOT NULL AND (
                                    -- Handle case where highway tag is a single value
                                    CASE
                                        WHEN typeof(tags['highway']) = 'VARCHAR' THEN
                                            tags['highway']::VARCHAR IN ('platform', 'services', 'rest_area')
                                        -- Handle case where highway tag is a list
                                        WHEN typeof(tags['highway']) = 'LIST' THEN
                                            list_has_any(tags['highway']::VARCHAR[], ['platform', 'services', 'rest_area']::VARCHAR[])
                                        ELSE FALSE
                                    END
                                )
                            )
                            OR (
                                tags['public_transport'] IS NOT NULL AND (
                                    CASE
                                        WHEN typeof(tags['public_transport']) = 'VARCHAR' THEN
                                            tags['public_transport']::VARCHAR = 'platform'
                                        WHEN typeof(tags['public_transport']) = 'LIST' THEN
                                            array_contains(tags['public_transport']::VARCHAR[], 'platform'::VARCHAR)
                                        ELSE FALSE
                                    END
                                )
                            )
                            OR (
                                tags['area'] IS NOT NULL AND (
                                    CASE
                                        WHEN typeof(tags['area']) = 'VARCHAR' THEN
                                            tags['area']::VARCHAR = 'yes'
                                        WHEN typeof(tags['area']) = 'LIST' THEN
                                            array_contains(tags['area']::VARCHAR[], 'yes'::VARCHAR)
                                        ELSE FALSE
                                    END
                                )
                            )
                        )
                        -- if the element is specifically tagged 'area':'no' -> LineString
                        AND NOT (
                            list_contains(map_keys(tags)::VARCHAR[], 'area'::VARCHAR)
                            AND (
                                CASE
                                    WHEN typeof(tags['area']) = 'VARCHAR' THEN
                                        tags['area']::VARCHAR = 'no'
                                    WHEN typeof(tags['area']) = 'LIST' THEN
                                        list_extract(map_extract(tags, 'area'), 1)::VARCHAR = 'no'
                                    ELSE FALSE
                                END
                            )
                        )
                    )
                    THEN ST_MakePolygon(linestring)
                    ELSE linestring
                END)::GEOMETRY AS geometry
            FROM matching_ways_linestrings;
        """)

        count = self.conn.execute(
            "SELECT COUNT(*) FROM ways_linestrings_polygons"
        ).fetchone()[0]
        logger.info(f"Processed {count} ways with geometries")

    def _process_relations(self):
        """Process relations and convert to complex geometries"""
        if self._table_exists("relations"):
            logger.info(
                "Table 'relations' already exists, skipping relation processing"
            )
            return

        logger.info("Processing relations")
        # Step 1: Extract relations with tags (multipolygons and boundaries)
        if not self._table_exists("matching_relations"):
            self.conn.execute("""
                CREATE TABLE matching_relations AS
                SELECT id, tags
                FROM osm
                WHERE kind = 'relation' AND len(refs) > 0
                    AND tags IS NOT NULL AND cardinality(tags) > 0
                    AND list_contains(map_keys(tags)::VARCHAR[], 'type'::VARCHAR)
                    AND list_has_any(map_extract(tags, 'type')::VARCHAR[], ['boundary', 'multipolygon']::VARCHAR[]);
            """)

        # Step 2: Extract way references for relations
        if not self._table_exists("matching_relations_with_ways_refs"):
            self.conn.execute("""
                CREATE TABLE matching_relations_with_ways_refs AS
                WITH unnested_relation_refs AS (
                    SELECT
                        r.id,
                        UNNEST(refs) as ref,
                        UNNEST(ref_types) as ref_type,
                        UNNEST(ref_roles) as ref_role,
                        UNNEST(range(length(refs))) as ref_idx
                    FROM osm r
                    SEMI JOIN matching_relations USING (id)
                    WHERE kind = 'relation'
                )
                SELECT id, ref, ref_role, ref_idx
                FROM unnested_relation_refs
                WHERE ref_type = 'way';
            """)

        # Step 3: Construct linestrings for the ways required by relations
        if not self._table_exists("required_ways_linestrings"):
            logger.info(
                "Creating linestrings for ways required by relations (in batches)"
            )

            # First, create the required tables for the batching process
            if not self._table_exists("ways_required_by_relations_with_nodes_refs"):
                self.conn.execute("""
                    CREATE TABLE ways_required_by_relations_with_nodes_refs AS
                    SELECT id, UNNEST(refs) as ref, UNNEST(range(length(refs))) as ref_idx
                    FROM osm ways
                    SEMI JOIN matching_relations_with_ways_refs
                    ON ways.id = matching_relations_with_ways_refs.ref
                    WHERE kind = 'way'
                """)

            if not self._table_exists("nodes_required_by_relations_with_geometries"):
                self.conn.execute("""
                    CREATE TABLE nodes_required_by_relations_with_geometries AS
                    SELECT id, ST_POINT(lon, lat) geometry
                    FROM osm nodes
                    SEMI JOIN ways_required_by_relations_with_nodes_refs
                    ON nodes.id = ways_required_by_relations_with_nodes_refs.ref
                    WHERE kind = 'node'
                """)

            # Get distinct way IDs for batching
            max_id = self.conn.execute("""
                SELECT MAX(id) FROM ways_required_by_relations_with_nodes_refs
            """).fetchone()[0]

            # Create initial batch
            self.conn.execute(f"""
                CREATE TABLE required_ways_linestrings AS
                SELECT
                    ways.id,
                    ST_MakeLine(list(nodes.geometry ORDER BY ref_idx ASC)) linestring
                FROM ways_required_by_relations_with_nodes_refs ways
                JOIN nodes_required_by_relations_with_geometries nodes
                ON ways.ref = nodes.id
                WHERE ways.id < {self.batch_size}
                GROUP BY 1;
            """)

            # Process remaining batches
            current_max = self.batch_size
            while current_max < max_id:
                logger.info(
                    f"Processing relation ways with IDs {current_max} to {current_max + self.batch_size}"
                )

                self.conn.execute(f"""
                    INSERT INTO required_ways_linestrings
                    SELECT
                        ways.id,
                        ST_MakeLine(list(nodes.geometry ORDER BY ref_idx ASC)) linestring
                    FROM ways_required_by_relations_with_nodes_refs ways
                    JOIN nodes_required_by_relations_with_geometries nodes
                    ON ways.ref = nodes.id
                    WHERE ways.id >= {current_max} AND ways.id < {current_max + self.batch_size}
                    GROUP BY 1;
                """)

                current_max += self.batch_size

        # Step 4: Join relations with way linestrings
        if not self._table_exists("matching_relations_with_ways_linestrings"):
            self.conn.execute("""
                CREATE TABLE matching_relations_with_ways_linestrings AS
                WITH unnested_relations_with_way_linestrings AS (
                    SELECT
                        r.id,
                        COALESCE(r.ref_role, 'outer') as ref_role,
                        r.ref,
                        w.linestring::GEOMETRY as geometry
                    FROM matching_relations_with_ways_refs r
                    JOIN required_ways_linestrings w
                    ON w.id = r.ref
                    ORDER BY r.id, r.ref_idx
                ),
                any_outer_refs AS (
                    -- check if any way attached to the relation has the `outer` role
                    SELECT id, bool_or(ref_role == 'outer') has_any_outer_refs
                    FROM unnested_relations_with_way_linestrings
                    GROUP BY id
                )
                SELECT
                    unnested_relations_with_way_linestrings.* EXCLUDE (ref_role),
                    -- if none of the way refs has `outer` role - treat each ref as `outer`
                    CASE WHEN any_outer_refs.has_any_outer_refs
                        THEN unnested_relations_with_way_linestrings.ref_role
                        ELSE 'outer'
                    END as ref_role
                FROM unnested_relations_with_way_linestrings
                JOIN any_outer_refs
                ON any_outer_refs.id = unnested_relations_with_way_linestrings.id;
            """)

        # Step 5: Merge linestrings into polygons
        if not self._table_exists("matching_relations_with_merged_polygons"):
            self.conn.execute("""
                CREATE TABLE matching_relations_with_merged_polygons AS
                WITH merged_linestrings AS (
                    SELECT
                        id,
                        ref_role,
                        UNNEST(
                            ST_Dump(ST_LineMerge(ST_Collect(list(geometry)))),
                            recursive := true
                        ),
                    FROM matching_relations_with_ways_linestrings
                    GROUP BY id, ref_role
                ),
                relations_with_linestrings AS (
                    SELECT
                        id,
                        ref_role,
                        -- ST_Dump returns column named `geom`
                        geom AS geometry,
                        row_number() OVER (PARTITION BY id) as geometry_id
                    FROM
                        merged_linestrings
                    -- discard linestrings with less than 4 points
                    WHERE ST_NPoints(geom) >= 4
                ),
                valid_relations AS (
                    SELECT id, is_valid
                    FROM (
                        SELECT
                            id,
                            bool_and(
                                -- Check if start point equals the end point
                                ST_Equals(ST_StartPoint(geometry), ST_EndPoint(geometry))
                            ) is_valid
                        FROM relations_with_linestrings
                        GROUP BY id
                    )
                    WHERE is_valid = true
                )
                SELECT
                    id,
                    ref_role,
                    ST_MakePolygon(geometry) geometry,
                    geometry_id
                FROM relations_with_linestrings
                SEMI JOIN valid_relations
                ON relations_with_linestrings.id = valid_relations.id;
            """)

        # Step 6: Handle inner and outer rings to create proper polygons
        logger.info("Creating relation polygons with inner holes")

        # Create temporary tables for the final step
        self.conn.execute("""
            CREATE TEMP TABLE matching_relations_with_outer_polygons_with_holes AS
            WITH outer_polygons AS (
                SELECT id, geometry_id, geometry
                FROM matching_relations_with_merged_polygons
                WHERE ref_role = 'outer'
            ), inner_polygons AS (
                SELECT id, geometry_id, geometry
                FROM matching_relations_with_merged_polygons
                WHERE ref_role = 'inner'
            )
            SELECT
                op.id,
                op.geometry_id,
                ST_Difference(any_value(op.geometry), ST_Union_Agg(ip.geometry)) geometry
            FROM outer_polygons op
            JOIN inner_polygons ip
            ON op.id = ip.id AND ST_WITHIN(ip.geometry, op.geometry)
            GROUP BY op.id, op.geometry_id;
        """)

        self.conn.execute("""
            CREATE TEMP TABLE matching_relations_with_outer_polygons_without_holes AS
            WITH outer_polygons AS (
                SELECT id, geometry_id, geometry
                FROM matching_relations_with_merged_polygons
                WHERE ref_role = 'outer'
            )
            SELECT
                op.id,
                op.geometry_id,
                op.geometry
            FROM outer_polygons op
            ANTI JOIN matching_relations_with_outer_polygons_with_holes opwh
            ON op.id = opwh.id AND op.geometry_id = opwh.geometry_id;
        """)

        # Step 7: Create final relations table
        logger.info("Creating final relations table")

        self.conn.execute("""
            CREATE TABLE relations AS
            WITH unioned_outer_geometries AS (
                SELECT id, geometry
                FROM matching_relations_with_outer_polygons_with_holes
                UNION ALL
                SELECT id, geometry
                FROM matching_relations_with_outer_polygons_without_holes
            ),
            final_geometries AS (
                SELECT id, ST_Union_Agg(geometry) geometry
                FROM unioned_outer_geometries
                GROUP BY id
            )
            SELECT r_g.id, r.tags, r_g.geometry
            FROM final_geometries r_g
            JOIN matching_relations r
            ON r.id = r_g.id;
        """)

        count = self.conn.execute("SELECT COUNT(*) FROM relations").fetchone()[0]
        logger.info(f"Processed {count} relations with geometries")

    def _optimize_for_spatial_queries(self):
        """Optimize tables for spatial queries"""
        if (
            self._table_exists("nodes_ord")
            and self._table_exists("ways_ord")
            and self._table_exists("relations_ord")
        ):
            logger.info(
                "Spatial optimization tables already exist, skipping optimization"
            )
            return

        logger.info("Optimizing tables for spatial queries")

        # Step 1: Add bounding box information and order by Hilbert curve
        logger.info("Adding bounding box information and ordering by Hilbert curve")

        if not self._table_exists("nodes_ord"):
            logger.info("Creating nodes_ord")

            self.conn.execute("""
                CREATE TABLE nodes_ord AS
                SELECT *,
                    st_xmin(geometry) as lon_min,
                    st_xmax(geometry) as lon_max,
                    st_ymin(geometry) as lat_min,
                    st_ymax(geometry) as lat_max
                FROM nodes
                ORDER BY ST_Hilbert(geometry, ST_Extent(ST_MakeEnvelope(-180, -90, 180, 90)));
            """)

        if not self._table_exists("ways_ord"):
            logger.info("Creating ways_ord")

            self.conn.execute("""
                CREATE TABLE ways_ord AS
                SELECT *,
                    st_xmin(geometry) as lon_min,
                    st_xmax(geometry) as lon_max,
                    st_ymin(geometry) as lat_min,
                    st_ymax(geometry) as lat_max
                FROM ways_linestrings_polygons
                ORDER BY ST_Hilbert(geometry, ST_Extent(ST_MakeEnvelope(-180, -90, 180, 90)));
            """)

        if not self._table_exists("relations_ord"):
            logger.info("Creating relations_ord")

            self.conn.execute("""
                CREATE TABLE relations_ord AS
                SELECT *,
                    st_xmin(geometry) as lon_min,
                    st_xmax(geometry) as lon_max,
                    st_ymin(geometry) as lat_min,
                    st_ymax(geometry) as lat_max
                FROM relations
                WHERE geometry IS NOT NULL
                ORDER BY ST_Hilbert(geometry, ST_Extent(ST_MakeEnvelope(-180, -90, 180, 90)));
            """)

        # Step 2: Create spatial indexes (R-trees)
        logger.info("Creating spatial indexes (R-trees)")

        # Create R-tree indexes directly on the tables
        self.conn.execute("""
            CREATE INDEX IF NOT EXISTS rel_sp ON relations_ord USING RTREE (geometry);
            CREATE INDEX IF NOT EXISTS node_sp ON nodes_ord USING RTREE (geometry);
            CREATE INDEX IF NOT EXISTS way_sp ON ways_ord USING RTREE (geometry);
        """)

        logger.info("Spatial optimization completed")
