# OSM Processor

A Python package for converting OpenStreetMap (OSM) PBF files to DuckDB with spatial types.

## Overview

This package processes OpenStreetMap PBF files and converts them into DuckDB tables with proper spatial types (points, linestrings, polygons) for efficient spatial queries. The processing pipeline includes:

1. Loading OSM data from PBF files
2. Processing nodes and converting them to point geometries
3. Processing ways and converting them to linestrings or polygons based on OSM tagging conventions
4. Processing relations (particularly multipolygons and boundaries) and creating complex geometries
5. Optimizing tables for spatial queries with bounding box information and Hilbert curve ordering

## Installation

### Prerequisites

- Python 3.8+
- DuckDB with spatial extension

### Install from source

```bash
# Clone the repository
git clone https://github.com/yourusername/osm-convert.git
cd osm-convert

# Install with uv (recommended)
uv pip install -e .

# Or with pip
pip install -e .
```

## Usage

### Command-line interface

```bash
# Basic usage
uv run main.py /path/to/your/osm.pbf

# Specify output database path
uv run main.py /path/to/your/osm.pbf -o /path/to/output.db

# Adjust batch size for processing ways (to avoid OOM errors)
uv run main.py /path/to/your/osm.pbf -b 10000000
```

### Python API

```python
from osm_convert.osm_processor import OSMProcessor

# Process an OSM PBF file
with OSMProcessor('/path/to/your/osm.pbf', '/path/to/output.db') as processor:
    processor.process()
```

## Output Database Structure

The resulting DuckDB database contains the following tables:

### Main tables

- `nodes`: Points with tags
- `ways_linestrings_polygons`: Ways as linestrings or polygons
- `relations`: Complex geometries from relations

### Optimized tables with spatial indexes

- `nodes_ord`: Nodes ordered by Hilbert curve with bounding box information and R-tree spatial index
- `ways_ord`: Ways ordered by Hilbert curve with bounding box information and R-tree spatial index
- `relations_ord`: Relations ordered by Hilbert curve with bounding box information and R-tree spatial index

## Spatial Features

This package leverages several advanced spatial features from DuckDB's spatial extension:

### ST_Hilbert Function

The `ST_Hilbert` function is used to order geometries using a Hilbert space-filling curve, which improves spatial locality for range queries. This helps with performance when querying data within specific geographic regions.

### R-Tree Spatial Indexes

R-tree spatial indexes are created on the geometry columns to accelerate spatial queries. These indexes allow for efficient filtering of geometries based on spatial relationships like intersection, containment, etc.

Benefits of R-tree indexes:
- Significantly faster spatial queries compared to full table scans
- Efficient filtering based on spatial predicates (ST_Intersects, ST_Within, etc.)
- Automatic use by the query optimizer when appropriate

## Example Queries

### Find all features within a bounding box

```sql
-- Fast query using bounding box filtering and Hilbert ordering
SELECT *
FROM ways_ord
WHERE lon_min <= -0.97 + 0.05
  AND lon_max >= -0.97 - 0.05
  AND lat_min <= 51.46 + 0.05
  AND lat_max >= 51.46 - 0.05;
```

### Find features using R-tree spatial index

```sql
-- This query will automatically use the R-tree index
SELECT *
FROM ways_ord
WHERE ST_Intersects(geometry, ST_MakeEnvelope(-0.98, 51.45, -0.96, 51.47));
```

### Find features by tag within a distance

```sql
-- Find railway features within 1km of a point
-- This query will use the R-tree index for the spatial filter
SELECT *
FROM ways_ord
WHERE ST_DWithin(geometry, ST_Point(-0.97, 51.46), 0.01)
  AND len(tags['railway']) > 0;
```

### Combine spatial and attribute filtering

```sql
-- Find buildings within a specific area
SELECT id, tags, geometry
FROM ways_ord
WHERE ST_Within(geometry, ST_MakeEnvelope(-0.98, 51.45, -0.96, 51.47))
  AND list_has_any(map_keys(tags), ['building']);

SELECT id, tags, geometry
FROM ways_ord
WHERE ST_Within(geometry, ST_MakeEnvelope(-0.98, 51.45, -0.96, 51.47))
  AND lat_min <= 51.47
  AND lon_min <= -0.96
  AND lat_max >= 51.45
  AND lon_max >= -0.98
  AND list_has_any(map_keys(tags), ['building']);
```

### Export to GeoJSON

```sql
-- Export to GeoJSON for visualization
COPY (
  SELECT geometry
  FROM ways_ord
  WHERE ST_DWithin(geometry, ST_Point(-0.97, 51.46), 0.01)
    AND len(tags['railway']) > 0
) TO '/path/to/output.json'
WITH (FORMAT GDAL, DRIVER 'GeoJSON');
```

### Analyze spatial data distribution

```sql
-- Count features by type within a region
SELECT
  CASE
    WHEN list_has_any(map_keys(tags), ['building']) THEN 'building'
    WHEN list_has_any(map_keys(tags), ['highway']) THEN 'highway'
    WHEN list_has_any(map_keys(tags), ['natural']) THEN 'natural'
    ELSE 'other'
  END AS feature_type,
  COUNT(*) AS count
FROM ways_ord
WHERE ST_Intersects(geometry, ST_MakeEnvelope(-1.0, 51.4, -0.9, 51.5))
GROUP BY feature_type
ORDER BY count DESC;
```
