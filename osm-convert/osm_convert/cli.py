#!/usr/bin/env python3
"""
Command-line interface for OSM Processor

This script provides a command-line interface to the OSM Processor,
which converts OSM PBF files to DuckDB with spatial types.
"""

import argparse
import logging
import sys
from pathlib import Path
from .osm_processor import OSMProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("osm_processor_cli")

def parse_args():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(
        description="Convert OSM PBF files to DuckDB with spatial types"
    )
    
    parser.add_argument(
        "pbf_path",
        type=str,
        help="Path to the OSM PBF file"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        default=None,
        help="Path to the output DuckDB database (default: <pbf_path>.db)"
    )
    
    parser.add_argument(
        "--batch-size", "-b",
        type=int,
        default=50000000,
        help="Batch size for processing ways (default: 50000000)"
    )
    
    return parser.parse_args()

def main():
    """Main entry point"""
    args = parse_args()
    
    # Validate PBF path
    pbf_path = Path(args.pbf_path)
    if not pbf_path.exists():
        logger.error(f"OSM PBF file not found: {pbf_path}")
        sys.exit(1)
    
    # Set output path
    if args.output:
        db_path = args.output
    else:
        db_path = f"{pbf_path}.db"
    
    # Process the OSM PBF file
    try:
        with OSMProcessor(pbf_path, db_path, args.batch_size) as processor:
            processor.process()
        logger.info(f"OSM processing completed successfully. Output: {db_path}")
    except Exception as e:
        logger.error(f"Error processing OSM PBF file: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
