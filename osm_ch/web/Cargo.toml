[package]
name = "osm_ch_web"
version = "1.0.0"
authors = [ "<PERSON> <<EMAIL>>" ]
edition = "2018"
include = [
  "README.md",
  ".gitignore",
  "Cargo.toml",
  "src/*.rs",
]

[dependencies]
actix-files = "0.6"
actix-web = "4.1"
bincode = "1.3"
delaunator = "1.0.2"
env_logger = "0.10"
geo = { version = "0.28.0", features = ["use-proj"] }
geo-offset = { git = "https://github.com/lelongg/geo-offset", branch = "master", version = "0.3.1-alpha.0" }
geo-types = "0.7.14"
geojson = { version = "0.24.1", features = ["geo-types"] }
intel-mkl-src = "0.8.1"
itertools = "0.12.1"
log = "0.4"
ndarray = { version = "0.15.6", features = ["rayon"] }
ndarray-linalg = { version = "0.16.0", features = ["intel-mkl"] }
numpy = "0.20.0"
pathfinding = "4.9.1"
rayon = "1.5"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"


[dev-dependencies]
approx = "0.5.1"
criterion = { version = "0.4.0", features = ["html_reports"] }
ndarray-rand = "0.14.0"
