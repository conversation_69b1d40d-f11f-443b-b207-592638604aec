use crate::structs::Node;
use crate::{grid, EdgeId, FmiFile, Way};
use actix_web::web;
use delaunator::{triangulate, Point};
use itertools::Itertools;
use ndarray::parallel::prelude::*;
use ndarray::*;
use ndarray_linalg::*;
use numpy::*;
use rayon::prelude::*;
use std::collections::HashSet;

/// Calculate the circumcentre of a set of points in barycentric coordinates.
pub fn circumcentre(points: ArrayView2<f32>) -> Option<Array1<f32>> {
    let n_rows = points.shape()[0];

    // Build the Coefficient matrix
    let matrix = concatenate![
        Axis(0),
        concatenate![
            Axis(1),
            2.0 * points.dot(&points.t()),
            Array::ones((n_rows, 1))
        ],
        concatenate![Axis(1), Array::ones((1, n_rows)), Array::zeros((1, 1))]
    ];

    // build the ordinate
    let ord = concatenate![
        Axis(0),
        (&points * &points).sum_axis(Axis(1)),
        Array::ones(1)
    ];

    // solve
    // TODO error handling here for failure to converge
    if let Ok(res) = matrix.solve_into(ord){
        return Some(res.slice(s![..-1]).to_owned())
    }
    None
}

/// Calculate the circumradius of a given set of points
pub fn circumradius(points: ArrayView2<f32>) -> Option<f32> {
    let slice = points.slice(s![0, ..]).to_owned();
    if let Some(centre) = circumcentre(points.view()){
        return Some((slice - centre.dot(&points)).norm())
    }
    None
}

/// Returns simplices of the given set of points
pub fn alpha_simplices(points: ArrayView2<f32>) -> Vec<i32> {
    let pts: Vec<Point> = points
        .axis_iter(Axis(0))
        .map(|arr| Point {
            x: arr[0] as f64,
            y: arr[1] as f64,
        })
        .collect();

    triangulate(&pts)
        .triangles
        .par_iter()
        .map(|x| *x as i32)
        .collect()
}

/// Get the indices of each valid simplex
pub fn get_edges(points: ArrayView2<f32>, tri: &[i32], alpha: f32) -> Vec<Vec<i32>> {
    // extract the coordinates and circumradius for this simplex triangle
    let coords = stack![
        Axis(0),
        points.slice(s![tri[0], ..]),
        points.slice(s![tri[1], ..]),
        points.slice(s![tri[2], ..]),
    ];
    let rad = circumradius(coords.view());

    // extract the indices of each point of the simplex
    let mut idxs: Vec<i32> = Vec::new();
    for c in coords.rows() {
        for (idx, p) in points.rows().into_iter().enumerate() {
            if p == c {
                idxs.push(idx as i32);
            }
        }
    }

    // add the points to edges if required
    let mut edges = Vec::new();

    if rad.is_some() && rad.unwrap() < 1.0 / alpha {
        for edge in idxs.into_iter().combinations(2) {
            edges.push(edge);
        }
    }

    edges
}

// Return the indices of the array that form the edges of the 2D alpha shape
pub fn alphashape_edges(points: ArrayView2<f32>, alpha: f32) -> Vec<Vec<i32>> {
    // extract the simplex triangles
    let simplexes: Vec<i32> = alpha_simplices(points.view());

    let edges: Vec<Vec<Vec<i32>>> = simplexes
        .par_chunks_exact(3)
        .map(|tri| get_edges(points, tri, alpha))
        .into_par_iter()
        .collect();

    edges
        .into_iter()
        .flatten()
        .collect::<HashSet<Vec<i32>>>()
        .into_iter()
        .collect()
}

pub fn alphashape_from_ways(
    edges: Vec<Way>,
    nodes: &Vec<Node>,
    alpha: f32,
) -> Vec<Vec<(f32, f32)>> {
    let tmp: Vec<f32> = edges
        .iter()
        .flat_map(|edge| vec![edge.source, edge.target])
        .unique()
        .flat_map(|node| vec![nodes[node].longitude, nodes[node].latitude])
        .collect();
    println!("POINTS VEC, len {}", tmp.len());
    // println!("{:?}", tmp);
    let yy = Array2::from_shape_vec((tmp.len() / 2, 2), tmp).unwrap();
    println!("ARRAY2, shape {:?}", yy.shape());
    // println!("{:?}", yy);
    let alpha_shape = alphashape_edges(yy.view(), alpha);
    alpha_shape
        .iter()
        .map(|point| {
            point
                .iter()
                .map(|&pt| (yy[[pt as usize, 0]], yy[[pt as usize, 1]]))
                .collect()
        })
        .collect()
}

#[cfg(test)]
mod tests {
    use super::*;
    use approx::assert_relative_eq;
    use ndarray::array;

    #[test]
    fn test_circumcentre() {
        let points = array![[1.0, 0.0], [0.5, 0.25], [0.0, 0.0]];
        let res = circumcentre(points.view()).unwrap();

        assert_relative_eq!(res[0], 1.25, epsilon = 1.0e-6);
        assert_relative_eq!(res[1], -1.5, epsilon = 1.0e-6);
        assert_relative_eq!(res[2], 1.25, epsilon = 1.0e-6);
    }

    #[test]
    fn test_circumradius() {
        let points = array![[1.0, 0.0], [0.5, 0.25], [0.0, 0.0]];
        let res = circumradius(points.view()).unwrap();

        assert_relative_eq!(res, 0.625, epsilon = 1.0e-6);
    }

    #[test]
    fn test_alpha_simplices() {
        let points = array![[1.0, 0.0], [0.5, 0.25], [0.0, 0.0]];
        let res = alpha_simplices(points.view());

        assert_eq!(res[0], 1);
        assert_eq!(res[1], 0);
        assert_eq!(res[2], 2);
    }

    #[test]
    fn test_alpha_shape_edges() {
        let points = array![
            [1.2126101, 52.81025],
            [1.2132691, 52.810196],
            [-0.9716508, 51.46754],
            [-0.9719247, 51.46754],
            [-0.9705689, 51.46762],
            [-0.9719238, 51.467472],
            [-0.9720395, 51.467537],
            [-0.9719228, 51.467403],
            [-0.9723649, 51.467518],
            [-0.9719584, 51.467346],
            [-0.9720509, 51.467205],
            [-0.9726958, 51.467476],
            [-0.9717166, 51.467125],
            [-0.9722976, 51.467262],
            [-0.972755, 51.46746],
            [-0.9705298, 51.46814],
            [-0.9705674, 51.467594],
        ];
        let alpha = 2.0;

        let mut expected = vec![
            [7, 4],
            [7, 3],
            [3, 4],
            [5, 4],
            [4, 0],
            [6, 1],
            [6, 5],
            [1, 5],
            [4, 5],
            [5, 7],
            [0, 6],
            [5, 2],
            [4, 6],
            [2, 7],
        ];

        let mut res = alphashape_edges(points.view(), alpha);

        assert_eq!(res.len(), expected.len());
        assert_eq!(res.sort(), expected.sort());
    }

    #[test]
    fn test_from_nodes() {
        let nodes = vec![
            Node {
                latitude: 50.123456,
                longitude: 10.432143,
                rank: 1,
            },
            Node {
                latitude: 51.123456,
                longitude: 11.432143,
                rank: 1,
            },
            Node {
                latitude: 50.123456,
                longitude: 11.432143,
                rank: 1,
            },
            Node {
                latitude: 52.123456,
                longitude: 10.432143,
                rank: 1,
            },
            Node {
                latitude: 51.123456,
                longitude: 14.432143,
                rank: 1,
            },
            Node {
                latitude: 52.123456,
                longitude: 11.432143,
                rank: 1,
            },
            Node {
                latitude: 50.123,
                longitude: 10.432143,
                rank: 1,
            },
            Node {
                latitude: 50.1234,
                longitude: 10.432143,
                rank: 1,
            },
            Node {
                latitude: 50.123456,
                longitude: 10.432143,
                rank: 1,
            },
        ];
        let edges = vec![
            Way {
                source: 0,
                target: 1,
                weight: 1,
                contracted_previous: None,
                contracted_next: None,
            },
            Way {
                source: 0,
                target: 1,
                weight: 1,
                contracted_previous: None,
                contracted_next: None,
            },
            Way {
                source: 2,
                target: 3,
                weight: 1,
                contracted_previous: None,
                contracted_next: None,
            },
            Way {
                source: 3,
                target: 4,
                weight: 1,
                contracted_previous: None,
                contracted_next: None,
            },
            Way {
                source: 0,
                target: 1,
                weight: 1,
                contracted_previous: None,
                contracted_next: None,
            },
            Way {
                source: 2,
                target: 4,
                weight: 1,
                contracted_previous: None,
                contracted_next: None,
            },
            Way {
                source: 2,
                target: 5,
                weight: 1,
                contracted_previous: None,
                contracted_next: None,
            },
            Way {
                source: 2,
                target: 6,
                weight: 1,
                contracted_previous: None,
                contracted_next: None,
            },
        ];
        let result = alphashape_from_ways(edges, &nodes, 0.4);
        println!("{:?}", result);
        let mut expected = vec![vec![(7., 4.)]];

        assert_eq!(result.len(), expected.len());
        assert_eq!(result, expected);
    }
}
