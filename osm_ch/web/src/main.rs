#[macro_use]
extern crate log; 

extern crate intel_mkl_src;
mod bidijkstra;
mod constants;
mod geojson;
mod graph_helper;
mod grid;
mod helper;
mod min_heap;
mod structs;
mod visited_list;
mod alpha_shapes;

use actix_web::{middleware, web, App, HttpServer};
use ::geojson::GeoJson;
use rayon::prelude::*;
use std::cell::RefCell;
use std::cmp::min;
use std::convert::TryFrom;
use std::path::Path;
use std::time::Instant;
use ndarray::{Array, Array2, ArrayView2, Ix2, Axis};

use bidijkstra::Dijkstra;
use constants::*;
use geojson::*;
use structs::*;

use geo_offset::Offset;
use geo::{line_string, polygon, ConvexHull, GeometryCollection, KNearestConcaveHull, LineString, MultiLineString, MultiPoint, Point, Simplify, Transform};
use geo::ConcaveHull;
use geo::proj;
use geo::BoundingRect;
// use geo_types::MultiLineString;

async fn shortest_path(
    request: web::Json<GeoJsonRequest>,
    data: web::Data<FmiFile>,
    dijkstra_cell: web::Data<RefCell<Dijkstra>>,
) -> web::Json<GeoJsonRespone> {
    let total_time = Instant::now();

    // extract points
    let features = &request.features;
    assert_eq!(features.len(), 2);

    let start_feature = &features[0].geometry.coordinates;
    let end_feature = &features[1].geometry.coordinates;
    assert_eq!(start_feature.len(), 2);
    assert_eq!(end_feature.len(), 2);

    let start = Node {
        longitude: start_feature[0],
        latitude: start_feature[1],
        rank: INVALID_RANK,
    };
    let end = Node {
        longitude: end_feature[0],
        latitude: end_feature[1],
        rank: INVALID_RANK,
    };
    debug!("Start: {},{}", start.latitude, start.longitude);
    debug!("End: {},{}", end.latitude, end.longitude);

    // search for clicked points
    let grid_time = Instant::now();
    let start_id: NodeId = grid::get_closest_point(
        start,
        &data.nodes,
        &data.grid,
        &data.grid_offset,
        &data.grid_bounds,
    );
    let end_id: NodeId = grid::get_closest_point(
        end,
        &data.nodes,
        &data.grid,
        &data.grid_offset,
        &data.grid_bounds,
    );
    debug!("start_id {}", start_id);
    debug!("end_id {}", end_id);
    info!(" Get node-ID in: {:?}", grid_time.elapsed());

    let mut dijkstra = dijkstra_cell.borrow_mut();

    let dijkstra_time = Instant::now();
    let tmp = dijkstra.find_path(
        start_id,
        end_id,
        &data.nodes,
        &data.edges,
        &data.up_offset,
        &data.down_offset,
        &data.down_index,
    );
    info!("    Dijkstra in: {:?}", dijkstra_time.elapsed());

    let result: Vec<(f32, f32)>;
    let mut cost: String = "".to_string();
    match tmp {
        Some((path, _, path_cost)) => {
            let nodes = grid::get_coordinates(path, &data.nodes);
            result = nodes
                .par_iter()
                .map(|node| (node.longitude, node.latitude))
                .collect::<Vec<(f32, f32)>>();
            match data.optimized_by {
                OptimizeBy::Time => {
                    if path_cost.trunc() >= 1.0 {
                        cost = path_cost.trunc().to_string();
                        cost.push_str(" h ");
                    }
                    cost.push_str(&format!("{:.0}", path_cost.fract() * 60.0));
                    cost.push_str(" min");
                }
                OptimizeBy::Distance => {
                    cost = format!("{:.2}", path_cost);
                    cost.push_str(" km");
                }
            };
        }
        None => {
            warn!("no path found");
            result = Vec::<(f32, f32)>::new();
            cost = "no path found".to_string();
        }
    }

    info!("        Overall: {:?}", total_time.elapsed());

    web::Json(GeoJsonRespone {
        // escaping the rust-type command to normal type string
        r#type: "FeatureCollection".to_string(),
        features: vec![FeatureResponse {
            r#type: "Feature".to_string(),
            geometry: GeometryResponse::LineString {
                coordinates: result,
            },
            properties: Some(Property { weight: cost }),
        }],
    })
}


async fn shortest_with_iso_hull(
    request: web::Json<GeoJsonRequestWithAlpha>,
    data: web::Data<FmiFile>,
    dijkstra_cell: web::Data<RefCell<Dijkstra>>,
) -> web::Json<GeoJson> {
    let total_time = Instant::now();

    // extract points
    let features = &request.features;
    assert_eq!(features.len(), 1);
    let alpha_param = request.alpha;
    println!("Alpha: {}", alpha_param);
    let start_feature = &features[0].geometry.coordinates;
    // let end_feature = &features[1].geometry.coordinates;
    assert_eq!(start_feature.len(), 2);
    // assert_eq!(end_feature.len(), 2);

    let start = Node {
        longitude: start_feature[0],
        latitude: start_feature[1],
        rank: INVALID_RANK,
    };
    // let end = Node {
    //     longitude: end_feature[0],
    //     latitude: end_feature[1],
    //     rank: INVALID_RANK,
    // };
    debug!("Start: {},{}", start.latitude, start.longitude);
    // debug!("End: {},{}", end.latitude, end.longitude);

    // search for clicked points
    let grid_time = Instant::now();
    let start_id: NodeId = grid::get_closest_point(
        start,
        &data.nodes,
        &data.grid,
        &data.grid_offset,
        &data.grid_bounds,
    );
    // let end_id: NodeId = grid::get_closest_point(
    //     end,
    //     &data.nodes,
    //     &data.grid,
    //     &data.grid_offset,
    //     &data.grid_bounds,
    // );
    debug!("start_id {}", start_id);
    // debug!("end_id {}", end_id);
    info!(" Get node-ID in: {:?}", grid_time.elapsed());

    let mut dijkstra = dijkstra_cell.borrow_mut();

    // let path_time = Instant::now();
    // let found_path = dijkstra.find_path(
    //     start_id,
    //     end_id,
    //     &data.nodes,
    //     &data.edges,
    //     &data.up_offset,
    //     &data.down_offset,
    //     &data.down_index,
    // );
    // info!("    Dijkstra in: {:?}", path_time.elapsed());
    // println!("Done calculating shortest path in {:?}", path_time.elapsed());

    let dijkstra_time = Instant::now();
    println!("Calculating iso");
    // Include max size
    // let iso_max_weight = min(found_path.expect("Invalid shortest path").1, 2 * DIST_MULTIPLICATOR);

    let found_iso = dijkstra.find_iso(
        start_id,
        alpha_param,
        &data.nodes,
        &data.edges,
        &data.up_offset,
        &data.down_offset,
        &data.down_index,
    );
    info!("    Iso in: {:?}", dijkstra_time.elapsed());

    // let result: Vec<Vec<(f32, f32)>>;
    let mut result: MultiLineString;
    let mut cost: String = "".to_string();
    let iso_cost = alpha_param as f32 * WALK_SCALE / DIST_MULTIPLICATOR as f32;
    if iso_cost.trunc() >= 1.0 {
        cost = iso_cost.trunc().to_string();
        cost.push_str(" h ");
    }
    cost.push_str(&format!("{:.0}", iso_cost.fract() * 60.0));
    cost.push_str(" min");
    println!("Done calculating iso in {:?}", dijkstra_time.elapsed());

    match found_iso {
        Some(edges) => {
            let edges: Vec<Way> = grid::get_ways(edges, &data.edges);

            result = MultiLineString::new(
                edges
                .par_iter()
                .map(|edge| {
                    LineString::new(
                        vec![
                            Point::new((&data.nodes[edge.source]).longitude.into(), (&data.nodes[edge.source]).latitude.into()).into(),
                            Point::new((&data.nodes[edge.target]).longitude.into(), (&data.nodes[edge.target]).latitude.into()).into()
                        ]
                    )
                })
                .collect::<Vec<geo::LineString<f64>>>()
            );
            // # EPSG:4326 is the standard lat/lon coordinate system
            // # EPSG:27700 is the british national grid coordinate system https://spatialreference.org/ref/epsg/27700/
        }
        None => {
            warn!("no path found");
            // result = Vec::<Vec<(f32, f32)>>::new();
            result = MultiLineString::new(vec![]);
            // alpha_lines = Vec::new();
            cost = "no path found".to_string();
        }
    }
    println!("Start first coord transform {:?}", dijkstra_time.elapsed());
    result.transform_crs_to_crs("EPSG:4326", "EPSG:27700").expect("Couldn't transform into UK coords");
    println!("Finish first coord transform, start dilation {:?}", dijkstra_time.elapsed());
    let mut buffered: geo::MultiPolygon = result.offset(60.0).expect("Couldn't buffer result");
    println!("Finish dilation, start second coord transform {:?}", dijkstra_time.elapsed());
    buffered.transform_crs_to_crs("EPSG:27700", "EPSG:4326").expect("Couldn't transform into lat/lon coords");
    println!("Bounding rect: {:?}", buffered.bounding_rect());
    println!("Finish second coord transform {:?}", dijkstra_time.elapsed());
    // let geojson: GeoJson = GeoJson::try_from(&buffered).expect("Couldn't convert multipolygon to geojson");
    let all: GeometryCollection = GeometryCollection::new_from(vec![result.into(), buffered.into()]);
    let geojson: GeoJson = GeoJson::try_from(&all).expect("Couldn't convert multipolygon to geojson");
    info!("        Overall: {:?}", total_time.elapsed());
    println!("Overall time: {:?}", total_time.elapsed());

    web::Json(geojson)

}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    //std::env::set_var("RUST_LOG", "debug");
    std::env::set_var("RUST_LOG", "actix_web=trace");
    std::env::set_var("RUST_BACKTRACE", "1");
    env_logger::init();

    // read file
    let filename = helper::get_filename();
    println!("Start reading from disk");
    let data: FmiFile = helper::read_from_disk(filename);
    println!("Finish reading from disk");

    let amount_nodes = data.nodes.len();
    println!("before data_ref");

    let data_ref = web::Data::new(data);

    // check for static-html folder
    if !Path::new("./html").exists() {
        eprintln!("./html/ directory not found");
        std::process::exit(1);
    }

    // start webserver
    println!("Starting server at: http://localhost:8080");
    HttpServer::new(move || {
        // initialize thread-local dijkstra
        let dijkstra = web::Data::new(RefCell::new(Dijkstra::new(amount_nodes)));
        App::new()
            .wrap(middleware::Logger::default())
            .app_data(web::JsonConfig::default().limit(1024))
            .app_data(data_ref.clone())
            .app_data(dijkstra)
            .service(web::resource("/dijkstra").route(web::post().to(shortest_with_iso_hull)))
            .service(actix_files::Files::new("/", "./html/").index_file("index.html"))
    })
    // TODO Only need 1 worker for local testing
    // more workers takes more memory...
    .workers(1)
    .bind("localhost:8080")?
    .run()
    .await
}
