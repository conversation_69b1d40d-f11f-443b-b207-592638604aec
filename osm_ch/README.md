# OSM-Contraction-Hierarchies
by <PERSON>

This project was part of "Lab Course: Algorithms for OSM Data".

This project implements "Contraction Hierarchies". Which is one of the best known speed-up techniques for shortest path calculations. As data source [OpenStreetMap](https://www.openstreetmap.org)-data is used.

![screenshot](./screenshot-osm_ch.png)

This repository consists of two programms:

## pre

This will parse the `*.osm.pbf` file into a `*.osm.pbf.fmi` file, which is needed for the `web`-program
Cropped OSM-data can be downloaded from [Geofabrik.de](https://download.geofabrik.de/index.html)

### dependecies

- `bincode` = exporting serialization
- `num_cpus` = get number of threads
- `osmpbfreader` = parsing the pbf file
- `rayon` = parallelization
- `serde` = serialization


### Compilation
`cargo build --release -p osm_ch_pre`

### Usage
`cargo run --release -p osm_ch_pre ./germany-latest.osm.pbf`

### Info

from different grahps the best performance was using two cores (`taskset -c 0,1 cargo run ...`). This may vary between grahps.

## web

is the webserver which provides the web-interface. (it needs the `*.osm.pbf.fmi`-file from the `pre`-programm.)

### dependecies

- `actix-files` = serving static files
- `actix-rt` = running actix
- `actix-web` = webserver
- `bincode` = exporting serialization
- `env_logger` = logging the webserver
- `rayon` = parallelization
- `serde` = serialization
- `serde_json` = serialization json

### Compilation

`cargo build --release -p osm_ch_web`

### Usage

```
cargo run --release -p osm_ch_web ./britain-and-ireland-latest.osm.pbf.fmi  
```
