name: osm-<PERSON><PERSON><PERSON>

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - name: Build pre
      run: |
        cargo build --verbose -p osm_ch_pre
    - name: Build web
      run: |
        cargo build --verbose -p osm_ch_web
    - name: Run pre tests
      run: |
        cargo test --verbose -p osm_ch_pre
    - name: Run web tests
      run: |
        cargo test --verbose -p osm_ch_web
